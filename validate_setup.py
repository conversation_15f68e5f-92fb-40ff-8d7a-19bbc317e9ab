#!/usr/bin/env python3
"""
Simple validation script to test the basic setup
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def test_imports():
    """Test that all modules can be imported"""
    print("Testing imports...")
    
    try:
        from indicators_polars.utils.config_loader import ConfigLoader
        print("✓ Config loader imported")
        
        from indicators_polars.core.config_manager import ConfigManager
        print("✓ Config manager imported")
        
        from indicators_polars.database.clickhouse_client import ClickHouseClient
        print("✓ ClickHouse client imported")
        
        from indicators_polars.core.indicator_engine import IndicatorEngine
        print("✓ Indicator engine imported")
        
        from indicators_polars.indicators.technical_indicators import TechnicalIndicators
        print("✓ Technical indicators imported")
        
        from indicators_polars.indicators.custom_indicators import CustomIndicators
        print("✓ Custom indicators imported")
        
        from indicators_polars.utils.monitoring import PerformanceMonitor
        print("✓ Performance monitor imported")
        
        print("All imports successful!")
        return True
        
    except Exception as e:
        print(f"✗ Import failed: {e}")
        return False


def test_config_loading():
    """Test configuration loading"""
    print("\nTesting configuration loading...")
    
    try:
        from indicators_polars.utils.config_loader import ConfigLoader
        from indicators_polars.core.config_manager import ConfigManager
        
        # Load config
        config_loader = ConfigLoader('config.yaml')
        config_dict = config_loader.load()
        print("✓ Configuration loaded from YAML")
        
        # Create config manager
        config_manager = ConfigManager(config_dict)
        print("✓ Configuration manager created")
        
        # Validate config
        is_valid = config_manager.validate()
        if is_valid:
            print("✓ Configuration validation passed")
        else:
            print("✗ Configuration validation failed")
            return False
        
        print("Configuration test successful!")
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False


def test_polars_talib():
    """Test polars_talib functionality"""
    print("\nTesting polars_talib...")
    
    try:
        import polars as pl
        import polars_talib as ta
        
        # Create test data with enough points for indicators
        data = {
            'price': [100.0 + i for i in range(50)]  # 50 data points
        }
        df = pl.DataFrame(data)
        
        # Test RSI
        rsi_result = df.with_columns(
            ta.rsi(pl.col('price'), timeperiod=14).alias('rsi')
        )
        print("✓ RSI calculation works")
        
        # Test SMA
        sma_result = df.with_columns(
            ta.sma(pl.col('price'), timeperiod=10).alias('sma')
        )
        print("✓ SMA calculation works")
        
        print("polars_talib test successful!")
        return True
        
    except Exception as e:
        print(f"✗ polars_talib test failed: {e}")
        return False


def test_database_config():
    """Test database configuration (without connecting)"""
    print("\nTesting database configuration...")
    
    try:
        from indicators_polars.utils.config_loader import ConfigLoader
        
        config_loader = ConfigLoader('config.yaml')
        config_dict = config_loader.load()
        
        db_config = config_dict.get('database', {}).get('clickhouse', {})
        
        required_fields = ['host', 'port', 'username', 'password', 'database']
        for field in required_fields:
            if field not in db_config:
                print(f"✗ Missing database field: {field}")
                return False
        
        print("✓ All required database fields present")
        print(f"  Host: {db_config['host']}")
        print(f"  Port: {db_config['port']}")
        print(f"  Database: {db_config['database']}")
        
        print("Database configuration test successful!")
        return True
        
    except Exception as e:
        print(f"✗ Database configuration test failed: {e}")
        return False


def main():
    """Run all validation tests"""
    print("IndicatorsUsingPolars Setup Validation")
    print("=" * 40)
    
    tests = [
        test_imports,
        test_config_loading,
        test_polars_talib,
        test_database_config
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 40)
    print(f"Validation Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ Setup validation successful! You can proceed with running the application.")
        return 0
    else:
        print("✗ Some tests failed. Please check the errors above.")
        return 1


if __name__ == '__main__':
    sys.exit(main())
