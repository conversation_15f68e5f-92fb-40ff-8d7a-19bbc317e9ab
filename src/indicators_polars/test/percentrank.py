import polars as pl

def main():
    # Load CSV
    df = pl.read_csv("src/indicators_polars/test/data.csv", try_parse_dates=True, separator=";")

    # Calculate percentrank on 'oi'
    n = len(df)  # total number of rows
    print(f"Total rows: {n}")

    #Calculate rank of oi
    df = df.with_columns(
        pl.col("int_value").rank("ordinal").alias("oi_rank")
    )
    # Calculate percentrank on 'oi'
    df = df.with_columns(
        ((pl.col("int_value").rank("ordinal") - 1) / (n - 1)).alias("oi_rank_percent")
    )
    df = df.with_columns(
                        pl.col("int_value")
                        .rank(method="ordinal")
                        .truediv(pl.len())
                        .alias("oi_rank_percent2")
                    )

    # Select required columns
    result = df.select(["timestamp", "int_value", "oi_rank", "oi_rank_percent", "oi_rank_percent2"])

    # Print last 10 rows
    print(result.tail(15))
    # print(result.head(10))


if __name__ == "__main__":
    main()
