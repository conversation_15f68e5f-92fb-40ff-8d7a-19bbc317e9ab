"""
Logging configuration with console and daily rotating file logging
"""

import logging
import logging.handlers
import os
from pathlib import Path
from typing import Dict, Any


class LoggingConfig:
    """Configure logging for the application with console and file handlers"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config.get('logging', {})
        self.setup_logging()
    
    def setup_logging(self):
        """Setup logging configuration"""
        # Create logs directory if it doesn't exist
        log_config = self.config.get('file', {})
        log_path = log_config.get('path', 'logs/indicators.log')
        log_dir = Path(log_path).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # Get log level
        log_level = getattr(logging, self.config.get('level', 'INFO').upper())
        
        # Create root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(log_level)
        
        # Clear existing handlers
        root_logger.handlers.clear()
        
        # Setup console handler
        if self.config.get('console', {}).get('enabled', True):
            self._setup_console_handler(root_logger)
        
        # Setup file handler
        if self.config.get('file', {}).get('enabled', True):
            self._setup_file_handler(root_logger)
    
    def _setup_console_handler(self, logger: logging.Logger):
        """Setup console logging handler"""
        console_config = self.config.get('console', {})
        console_handler = logging.StreamHandler()
        
        console_format = console_config.get(
            'format', 
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_formatter = logging.Formatter(console_format)
        console_handler.setFormatter(console_formatter)
        
        logger.addHandler(console_handler)
    
    def _setup_file_handler(self, logger: logging.Logger):
        """Setup rotating file logging handler"""
        file_config = self.config.get('file', {})
        log_path = file_config.get('path', 'logs/indicators.log')
        
        # Setup rotating file handler
        rotation_config = file_config.get('rotation', {})
        file_handler = logging.handlers.TimedRotatingFileHandler(
            filename=log_path,
            when=rotation_config.get('when', 'midnight'),
            interval=rotation_config.get('interval', 1),
            backupCount=rotation_config.get('backup_count', 30),
            encoding=rotation_config.get('encoding', 'utf-8')
        )
        
        file_format = file_config.get(
            'format',
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        file_formatter = logging.Formatter(file_format)
        file_handler.setFormatter(file_formatter)
        
        logger.addHandler(file_handler)
    
    @staticmethod
    def get_logger(name: str) -> logging.Logger:
        """Get a logger instance"""
        return logging.getLogger(name)


def setup_logging(config: Dict[str, Any]) -> logging.Logger:
    """Setup logging and return main logger"""
    LoggingConfig(config)
    return LoggingConfig.get_logger('indicators_polars')
