# Output Columns Configuration
# This file defines all output columns for the indicators system
# It serves as the single source of truth for schema definition

# VALID TYPES:
# - Utf8: String/text data
# - Float32: 32-bit floating point numbers
# - Float64: 64-bit floating point numbers
# - UInt8, UInt16, UInt32, UInt64: Unsigned integers
# - Int8, Int16, Int32, Int64: Signed integers
# - Datetime: Date and time
# - Date: Date only
# - Boolean: True/false values

# VALID CATEGORIES:
# - key_columns: Primary identification columns (required)
# - original_data: Source data from input table
# - technical_indicators: RSI, MACD, SMA, EMA, etc.
# - ranking_indicators: Percentile rankings
# - custom_indicators: Business logic indicators
# - pattern_detection: Pattern recognition columns
# - trading_signals: Buy/sell signals
# - metadata: System metadata

# Column Categories and Metadata
column_categories:
  key_columns:
    description: "Primary key columns for data identification"
    required: true

  original_data:
    description: "Original data columns from source table"
    required: false

  technical_indicators:
    description: "Technical analysis indicators (RSI, MACD, SMA, EMA)"
    required: false

  ranking_indicators:
    description: "Percentile ranking indicators"
    required: false

  custom_indicators:
    description: "Custom business logic indicators"
    required: false

  pattern_detection:
    description: "Pattern and signal detection columns"
    required: false

  trading_signals:
    description: "Buy/sell signal columns"
    required: false

  metadata:
    description: "System metadata columns"
    required: true

# Output Columns Definition
# Format: column_name: {polars_type: type, clickhouse_type: type, nullable: bool, category: category_name, description: text, default: value}
# NOTE: Column order in this section determines the final DataFrame column order
columns:
  # Key columns (required)
  symbol:
    polars_type: "Utf8"
    clickhouse_type: "Enum8('UNKNOWN'=0, 'NIFTY'=1, 'BANKNIFTY'=2, 'FINNIFTY'=3, 'MIDCPNIFTY'=4)"
    nullable: false
    category: "key_columns"
    description: "Trading symbol (NIFTY, BANKNIFTY, etc.)"
    required: true

  timestamp:
    polars_type: "Datetime"
    clickhouse_type: "DateTime"
    nullable: false
    category: "key_columns"
    description: "Data timestamp"
    required: true

  expiry_date:
    polars_type: "Date"
    clickhouse_type: "Date"
    nullable: false
    category: "key_columns"
    description: "Option expiry date"
    required: true

  strike:
    polars_type: "Float32"
    clickhouse_type: "Float32"
    nullable: false
    category: "key_columns"
    description: "Strike price"
    required: true

  expiry_type:
    polars_type: "Utf8"
    clickhouse_type: "Enum8('UNKNOWN'=0, 'CE'=1, 'PE'=2, 'FUTURE'=3)"
    nullable: false
    category: "key_columns"
    description: "Option type (CE, PE, FUTURE)"
    required: true

  # Original data columns
  current_price:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "original_data"
    description: "Current underlying price"
    default: 0.0

  last_price:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "original_data"
    description: "Last traded price"
    default: 0.0

  oi:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "original_data"
    description: "Open interest"
    default: 0.0

  volume:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "original_data"
    description: "Trading volume"
    default: 0.0

  delta_volume:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "original_data"
    description: "Change in volume"
    default: 0.0

  delta:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "original_data"
    description: "Option delta"
    default: 0.0

  gamma:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "original_data"
    description: "Option gamma"
    default: 0.0

  theta:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "original_data"
    description: "Option theta"
    default: 0.0

  vega:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "original_data"
    description: "Option vega"
    default: 0.0

  iv:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "original_data"
    description: "Implied volatility"
    default: 0.0

  # # Technical indicators - RSI
  # rsi_last_price:
  #   polars_type: "Float32"
  #   clickhouse_type: "Nullable(Float32)"
  #   nullable: true
  #   category: "technical_indicators"
  #   description: "RSI calculated on last_price"
  #   default: 0.0
    
  # rsi_current_price:
  #   polars_type: "Float32"
  #   clickhouse_type: "Nullable(Float32)"
  #   nullable: true
  #   category: "technical_indicators"
  #   description: "RSI calculated on current_price"
  #   default: 0.0

  # # Technical indicators - MACD
  # macd_last_price:
  #   polars_type: "Float32"
  #   clickhouse_type: "Nullable(Float32)"
  #   nullable: true
  #   category: "technical_indicators"
  #   description: "MACD line for last_price"
  #   default: 0.0
    
  # macd_signal_last_price:
  #   polars_type: "Float32"
  #   clickhouse_type: "Nullable(Float32)"
  #   nullable: true
  #   category: "technical_indicators"
  #   description: "MACD signal line for last_price"
  #   default: 0.0
    
  # macd_histogram_last_price:
  #   polars_type: "Float32"
  #   clickhouse_type: "Nullable(Float32)"
  #   nullable: true
  #   category: "technical_indicators"
  #   description: "MACD histogram for last_price"
  #   default: 0.0

  # # Technical indicators - SMA (Simple Moving Average)
  # sma_5_last_price:
  #   polars_type: "Float32"
  #   clickhouse_type: "Nullable(Float32)"
  #   nullable: true
  #   category: "technical_indicators"
  #   description: "5-period SMA of last_price"
  #   default: 0.0
    
  # sma_10_last_price:
  #   polars_type: "Float32"
  #   clickhouse_type: "Nullable(Float32)"
  #   nullable: true
  #   category: "technical_indicators"
  #   description: "10-period SMA of last_price"
  #   default: 0.0
    
  # sma_20_last_price:
  #   polars_type: "Float32"
  #   clickhouse_type: "Nullable(Float32)"
  #   nullable: true
  #   category: "technical_indicators"
  #   description: "20-period SMA of last_price"
  #   default: 0.0
    
  # sma_50_last_price:
  #   polars_type: "Float32"
  #   clickhouse_type: "Nullable(Float32)"
  #   nullable: true
  #   category: "technical_indicators"
  #   description: "50-period SMA of last_price"
  #   default: 0.0
    
  # sma_5_oi:
  #   polars_type: "Float32"
  #   clickhouse_type: "Nullable(Float32)"
  #   nullable: true
  #   category: "technical_indicators"
  #   description: "5-period SMA of open interest"
  #   default: 0.0
    
  # sma_10_oi:
  #   polars_type: "Float32"
  #   clickhouse_type: "Nullable(Float32)"
  #   nullable: true
  #   category: "technical_indicators"
  #   description: "10-period SMA of open interest"
  #   default: 0.0
    
  # sma_20_oi:
  #   polars_type: "Float32"
  #   clickhouse_type: "Nullable(Float32)"
  #   nullable: true
  #   category: "technical_indicators"
  #   description: "20-period SMA of open interest"
  #   default: 0.0
    
  # sma_50_oi:
  #   polars_type: "Float32"
  #   clickhouse_type: "Nullable(Float32)"
  #   nullable: true
  #   category: "technical_indicators"
  #   description: "50-period SMA of open interest"
  #   default: 0.0
    
  # sma_5_volume:
  #   polars_type: "Float32"
  #   clickhouse_type: "Nullable(Float32)"
  #   nullable: true
  #   category: "technical_indicators"
  #   description: "5-period SMA of volume"
  #   default: 0.0
    
  # sma_10_volume:
  #   polars_type: "Float32"
  #   clickhouse_type: "Nullable(Float32)"
  #   nullable: true
  #   category: "technical_indicators"
  #   description: "10-period SMA of volume"
  #   default: 0.0
    
  # sma_20_volume:
  #   polars_type: "Float32"
  #   clickhouse_type: "Nullable(Float32)"
  #   nullable: true
  #   category: "technical_indicators"
  #   description: "20-period SMA of volume"
  #   default: 0.0
    
  # sma_50_volume:
  #   polars_type: "Float32"
  #   clickhouse_type: "Nullable(Float32)"
  #   nullable: true
  #   category: "technical_indicators"
  #   description: "50-period SMA of volume"
  #   default: 0.0

  # # Technical indicators - EMA (Exponential Moving Average)
  # ema_5_last_price:
  #   polars_type: "Float32"
  #   clickhouse_type: "Nullable(Float32)"
  #   nullable: true
  #   category: "technical_indicators"
  #   description: "5-period EMA of last_price"
  #   default: 0.0
    
  # ema_10_last_price:
  #   polars_type: "Float32"
  #   clickhouse_type: "Nullable(Float32)"
  #   nullable: true
  #   category: "technical_indicators"
  #   description: "10-period EMA of last_price"
  #   default: 0.0
    
  # ema_20_last_price:
  #   polars_type: "Float32"
  #   clickhouse_type: "Nullable(Float32)"
  #   nullable: true
  #   category: "technical_indicators"
  #   description: "20-period EMA of last_price"
  #   default: 0.0
    
  # ema_50_last_price:
  #   polars_type: "Float32"
  #   clickhouse_type: "Nullable(Float32)"
  #   nullable: true
  #   category: "technical_indicators"
  #   description: "50-period EMA of last_price"
  #   default: 0.0
    
  # ema_5_oi:
  #   polars_type: "Float32"
  #   clickhouse_type: "Nullable(Float32)"
  #   nullable: true
  #   category: "technical_indicators"
  #   description: "5-period EMA of open interest"
  #   default: 0.0
    
  # ema_10_oi:
  #   polars_type: "Float32"
  #   clickhouse_type: "Nullable(Float32)"
  #   nullable: true
  #   category: "technical_indicators"
  #   description: "10-period EMA of open interest"
  #   default: 0.0
    
  # ema_20_oi:
  #   polars_type: "Float32"
  #   clickhouse_type: "Nullable(Float32)"
  #   nullable: true
  #   category: "technical_indicators"
  #   description: "20-period EMA of open interest"
  #   default: 0.0
    
  # ema_50_oi:
  #   polars_type: "Float32"
  #   clickhouse_type: "Nullable(Float32)"
  #   nullable: true
  #   category: "technical_indicators"
  #   description: "50-period EMA of open interest"
  #   default: 0.0
    
  # ema_5_volume:
  #   polars_type: "Float32"
  #   clickhouse_type: "Nullable(Float32)"
  #   nullable: true
  #   category: "technical_indicators"
  #   description: "5-period EMA of volume"
  #   default: 0.0
    
  # ema_10_volume:
  #   polars_type: "Float32"
  #   clickhouse_type: "Nullable(Float32)"
  #   nullable: true
  #   category: "technical_indicators"
  #   description: "10-period EMA of volume"
  #   default: 0.0
    
  # ema_20_volume:
  #   polars_type: "Float32"
  #   clickhouse_type: "Nullable(Float32)"
  #   nullable: true
  #   category: "technical_indicators"
  #   description: "20-period EMA of volume"
  #   default: 0.0
    
  # ema_50_volume:
  #   polars_type: "Float32"
  #   clickhouse_type: "Nullable(Float32)"
  #   nullable: true
  #   category: "technical_indicators"
  #   description: "50-period EMA of volume"
  #   default: 0.0

  # Ranking indicators
  oi_rank:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "ranking_indicators"
    description: "Percentile rank of open interest"
    default: 0.0
    
  gamma_rank:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "ranking_indicators"
    description: "Percentile rank of gamma"
    default: 0.0
    
  delta_rank:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "ranking_indicators"
    description: "Percentile rank of delta"
    default: 0.0
    
  theta_rank:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "ranking_indicators"
    description: "Percentile rank of theta"
    default: 0.0
    
  vega_rank:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "ranking_indicators"
    description: "Percentile rank of vega"
    default: 0.0
    
  iv_rank:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "ranking_indicators"
    description: "Percentile rank of implied volatility"
    default: 0.0
    
  delta_volume_rank:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "ranking_indicators"
    description: "Percentile rank of delta volume"
    default: 0.0
    
  oi_per_rank:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "ranking_indicators"
    description: "Percentile rank of OI (alternative method)"
    default: 0.0
    
  gamma_per_rank:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "ranking_indicators"
    description: "Percentile rank of gamma (alternative method)"
    default: 0.0
    
  delta_per_rank:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "ranking_indicators"
    description: "Percentile rank of delta (alternative method)"
    default: 0.0
    
  theta_per_rank:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "ranking_indicators"
    description: "Percentile rank of theta (alternative method)"
    default: 0.0
    
  vega_per_rank:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "ranking_indicators"
    description: "Percentile rank of vega (alternative method)"
    default: 0.0
    
  iv_per_rank:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "ranking_indicators"
    description: "Percentile rank of IV (alternative method)"
    default: 0.0
    
  delta_volume_per_rank:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "ranking_indicators"
    description: "Percentile rank of delta volume (alternative method)"
    default: 0.0

  # Custom indicators
  intrinsic_value:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "custom_indicators"
    description: "Calculated intrinsic value"
    default: 0.0
    
  intrinsic_value_rank:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "custom_indicators"
    description: "Percentile rank of intrinsic value"
    default: 0.0
    
  intrinsic_value_per_rank:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "custom_indicators"
    description: "Percentile rank of intrinsic value (alternative method)"
    default: 0.0
    
  premium:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "custom_indicators"
    description: "Option premium calculation"
    default: 0.0
    
  premium_rank:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "custom_indicators"
    description: "Percentile rank of premium"
    default: 0.0
    
  premium_per_rank:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "custom_indicators"
    description: "Percentile rank of premium (alternative method)"
    default: 0.0
    
  price_change_pct:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "custom_indicators"
    description: "Percentage change in price"
    default: 0.0
    
  oi_change:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "custom_indicators"
    description: "Change in open interest"
    default: 0.0

  # Pattern detection and signals
  consecutive_price_drop:
    polars_type: "UInt32"
    clickhouse_type: "Nullable(UInt32)"
    nullable: true
    category: "pattern_detection"
    description: "Count of consecutive price drops"
    default: 0

  consecutive_oi_build:
    polars_type: "UInt32"
    clickhouse_type: "Nullable(UInt32)"
    nullable: true
    category: "pattern_detection"
    description: "Count of consecutive OI builds"
    default: 0

  candle_dip:
    polars_type: "UInt32"
    clickhouse_type: "Nullable(UInt32)"
    nullable: true
    category: "pattern_detection"
    description: "Candle dip pattern indicator"
    default: 0

  candle_rise:
    polars_type: "UInt32"
    clickhouse_type: "Nullable(UInt32)"
    nullable: true
    category: "pattern_detection"
    description: "Candle rise pattern indicator"
    default: 0

  candle_dip_signal:
    polars_type: "UInt32"
    clickhouse_type: "Nullable(UInt32)"
    nullable: true
    category: "pattern_detection"
    description: "Candle dip signal indicator"
    default: 0

  candle_rise_signal:
    polars_type: "UInt32"
    clickhouse_type: "Nullable(UInt32)"
    nullable: true
    category: "pattern_detection"
    description: "Candle rise signal indicator"
    default: 0

  # Trading signal indicators
  ints_value_rise_std:
    polars_type: "UInt32"
    clickhouse_type: "Nullable(UInt32)"
    nullable: true
    category: "trading_signals"
    description: "Intrinsic value rise standard signal"
    default: 0

  ints_value_rise_ultra:
    polars_type: "UInt32"
    clickhouse_type: "Nullable(UInt32)"
    nullable: true
    category: "trading_signals"
    description: "Intrinsic value rise ultra signal"
    default: 0

  ints_value_drop:
    polars_type: "UInt32"
    clickhouse_type: "Nullable(UInt32)"
    nullable: true
    category: "trading_signals"
    description: "Intrinsic value drop signal"
    default: 0

  premium_rise:
    polars_type: "UInt32"
    clickhouse_type: "Nullable(UInt32)"
    nullable: true
    category: "trading_signals"
    description: "Premium rise signal"
    default: 0

  premium_drop:
    polars_type: "UInt32"
    clickhouse_type: "Nullable(UInt32)"
    nullable: true
    category: "trading_signals"
    description: "Premium drop signal"
    default: 0

  gamma_rise:
    polars_type: "UInt32"
    clickhouse_type: "Nullable(UInt32)"
    nullable: true
    category: "trading_signals"
    description: "Gamma rise signal"
    default: 0

  del_vol_rise:
    polars_type: "UInt32"
    clickhouse_type: "Nullable(UInt32)"
    nullable: true
    category: "trading_signals"
    description: "Delta volume rise signal"
    default: 0

  oi_rise:
    polars_type: "UInt32"
    clickhouse_type: "Nullable(UInt32)"
    nullable: true
    category: "trading_signals"
    description: "Open interest rise signal"
    default: 0

  oi_drop:
    polars_type: "UInt32"
    clickhouse_type: "Nullable(UInt32)"
    nullable: true
    category: "trading_signals"
    description: "Open interest drop signal"
    default: 0

  # Spark indicators (momentum indicators)
  int_value_spark:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "custom_indicators"
    description: "Intrinsic value spark indicator"
    default: 0.0

  delta_spark:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "custom_indicators"
    description: "Delta spark indicator"
    default: 0.0

  delta_volume_spark:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "custom_indicators"
    description: "Delta volume spark indicator"
    default: 0.0

  iv_spark:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "custom_indicators"
    description: "Implied volatility spark indicator"
    default: 0.0

  vega_spark:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "custom_indicators"
    description: "Vega spark indicator"
    default: 0.0

  gamma_spark:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "custom_indicators"
    description: "Gamma spark indicator"
    default: 0.0

  # # Signal counts
  # standard_buy_count:
  #   polars_type: "UInt32"
  #   clickhouse_type: "Nullable(UInt32)"
  #   nullable: true
  #   category: "trading_signals"
  #   description: "Count of standard buy signals"
  #   default: 0

  # special_buy_count:
  #   polars_type: "UInt32"
  #   clickhouse_type: "Nullable(UInt32)"
  #   nullable: true
  #   category: "trading_signals"
  #   description: "Count of special buy signals"
  #   default: 0

  # sell_count:
  #   polars_type: "UInt32"
  #   clickhouse_type: "Nullable(UInt32)"
  #   nullable: true
  #   category: "trading_signals"
  #   description: "Count of sell signals"
  #   default: 0

  # Primary trading signals
  buy_primary:
    polars_type: "Utf8"
    clickhouse_type: "Nullable(LowCardinality(String))"
    nullable: true
    category: "trading_signals"
    description: "Primary buy signal"
    default: ""

  buy_primary_weight:
    polars_type: "Float32"
    clickhouse_type: "Nullable(Float32)"
    nullable: true
    category: "trading_signals"
    description: "Weight of primary buy signal"
    default: 0.0

  buy_special:
    polars_type: "Utf8"
    clickhouse_type: "Nullable(LowCardinality(String))"
    nullable: true
    category: "trading_signals"
    description: "Special buy signal (note: matches ClickHouse typo)"
    default: ""

  buy_special_desc:
    polars_type: "Utf8"
    clickhouse_type: "Nullable(LowCardinality(String))"
    nullable: true
    category: "trading_signals"
    description: "Description of special buy signal"
    default: ""

  short_covering_move:
    polars_type: "Utf8"
    clickhouse_type: "Nullable(LowCardinality(String))"
    nullable: true
    category: "trading_signals"
    description: "Short covering move indicator"
    default: ""

  short_covering_desc:
    polars_type: "Utf8"
    clickhouse_type: "Nullable(LowCardinality(String))"
    nullable: true
    category: "trading_signals"
    description: "Description of short covering move"
    default: ""

  # Special conditions
  # iv_vega_condition:
  #   polars_type: "Utf8"
  #   clickhouse_type: "Nullable(LowCardinality(String))"
  #   nullable: true
  #   category: "custom_indicators"
  #   description: "IV-Vega condition indicator"
  #   default: ""

  # gamma_condition:
  #   polars_type: "Utf8"
  #   clickhouse_type: "Nullable(LowCardinality(String))"
  #   nullable: true
  #   category: "custom_indicators"
  #   description: "Gamma condition indicator"
  #   default: ""

  # Metadata
  calculation_timestamp:
    polars_type: "Datetime"
    clickhouse_type: "DateTime DEFAULT now()"
    nullable: false
    category: "metadata"
    description: "Timestamp when indicators were calculated"
    required: false  # Not required in input data, added during processing

# NOTE: Column ordering is determined by the order of columns in the 'columns' section above
# This matches the ClickHouse table structure and eliminates the need for a separate column_order list
