"""
Configuration loader utility
"""

import yaml
from pathlib import Path
from typing import Dict, Any, Optional
import logging


logger = logging.getLogger(__name__)


class ConfigLoader:
    """Load and validate configuration from YAML file"""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = Path(config_path)
        self._config: Optional[Dict[str, Any]] = None
    
    def load(self) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        if not self.config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                self._config = yaml.safe_load(file)
            
            logger.info(f"Configuration loaded from {self.config_path}")
            self._validate_config()
            return self._config
            
        except yaml.YAMLError as e:
            raise ValueError(f"Invalid YAML configuration: {e}")
        except Exception as e:
            raise RuntimeError(f"Failed to load configuration: {e}")
    
    def _validate_config(self):
        """Validate required configuration sections"""
        if not self._config:
            raise ValueError("Configuration is empty")
        
        required_sections = ['database', 'market', 'processing', 'indicators']
        missing_sections = [section for section in required_sections 
                          if section not in self._config]
        
        if missing_sections:
            raise ValueError(f"Missing required configuration sections: {missing_sections}")
        
        # Validate database configuration
        db_config = self._config.get('database', {}).get('clickhouse', {})
        required_db_fields = ['host', 'port', 'username', 'password', 'database']
        missing_db_fields = [field for field in required_db_fields 
                           if field not in db_config]
        
        if missing_db_fields:
            raise ValueError(f"Missing required database fields: {missing_db_fields}")
        
        # Validate market configuration
        market_config = self._config.get('market', {})
        if not market_config.get('symbols'):
            raise ValueError("Market symbols configuration is required")
        
        # Validate processing configuration
        processing_config = self._config.get('processing', {})
        if processing_config.get('mode') not in ['live', 'historical']:
            raise ValueError("Processing mode must be 'live' or 'historical'")
        
        logger.info("Configuration validation passed")
    
    @property
    def config(self) -> Dict[str, Any]:
        """Get loaded configuration"""
        if self._config is None:
            raise RuntimeError("Configuration not loaded. Call load() first.")
        return self._config
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """Get configuration value using dot notation (e.g., 'database.clickhouse.host')"""
        if self._config is None:
            raise RuntimeError("Configuration not loaded. Call load() first.")
        
        keys = key_path.split('.')
        value = self._config
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        
        return value
