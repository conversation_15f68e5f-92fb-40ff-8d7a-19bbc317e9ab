"""
Polars schemas for ClickHouse table structures and data validation
"""

import polars as pl
from typing import Dict, Any, List, Optional
from enum import Enum
import yaml
from pathlib import Path
import logging


class SymbolEnum(Enum):
    """Symbol enumeration matching ClickHouse enum"""
    UNKNOWN = 0
    NIFTY = 1
    BANKNIFTY = 2
    FINNIFTY = 3
    MIDCPNIFTY = 4


class ExpiryTypeEnum(Enum):
    """Expiry type enumeration matching ClickHouse enum"""
    UNKNOWN = 0
    CE = 1
    PE = 2
    FUTURE = 3


class StrikeTypeEnum(Enum):
    """Strike type enumeration matching ClickHouse enum"""
    UNKNOWN = 0
    NEAR = 1
    FAR = 2
    FUTURE = 3


class MoneynessEnum(Enum):
    """Moneyness enumeration matching ClickHouse enum"""
    UNKNOWN = 0
    ITM = 1
    OTM = 2
    DOTM = 3
    DITM = 4
    ATM = 5


# Input data schema (from ClickHouse source table)
INPUT_SCHEMA = {
    'symbol': pl.Utf8,
    'timestamp': pl.Datetime,
    'expiry_date': pl.Date,
    'current_price': pl.Float32,
    'strike': pl.Float32,
    'strike_type': pl.Utf8,
    'lot_size': pl.UInt16,
    'expiry_type': pl.Utf8,
    'moneyness': pl.Utf8,
    'last_price': pl.Float32,
    'ltp_change': pl.Float32,
    'oi': pl.Float32,
    'oi_change': pl.Float32,
    'volume': pl.Float32,
    'is_liquid': pl.UInt8,
    'theta': pl.Float32,
    'delta': pl.Float32,
    'gamma': pl.Float32,
    'vega': pl.Float32,
    'iv': pl.Float32,
    'batch': pl.UInt32,
    'prev_current_price': pl.Float32,
    'prev_last_price': pl.Float32,
    'prev_oi': pl.Float32,
    'prev_volume': pl.Float32,
    'prev_delta': pl.Float32,
    'prev_gamma': pl.Float32,
    'prev_iv': pl.Float32,
    'delta_current_price': pl.Float32,
    'delta_last_price': pl.Float32,
    'delta_oi': pl.Float32,
    'delta_volume': pl.Float32,
    'delta_delta': pl.Float32,
    'delta_gamma': pl.Float32,
    'delta_iv': pl.Float32
}

# Legacy OUTPUT_SCHEMA - DEPRECATED
# Use OutputSchemaManager.get_output_schema() instead
# This is kept temporarily for backward compatibility
def get_legacy_output_schema():
    """
    DEPRECATED: Use OutputSchemaManager.get_output_schema() instead
    This function provides backward compatibility for existing code
    """
    import warnings
    warnings.warn(
        "OUTPUT_SCHEMA is deprecated. Use OutputSchemaManager.get_output_schema() instead.",
        DeprecationWarning,
        stacklevel=2
    )
    return OutputSchemaManager.get_output_schema()

# For backward compatibility, create OUTPUT_SCHEMA as a property
class _LegacySchemaProxy:
    def __getitem__(self, key):
        return OutputSchemaManager.get_output_schema()[key]

    def items(self):
        return OutputSchemaManager.get_output_schema().items()

    def keys(self):
        return OutputSchemaManager.get_output_schema().keys()

    def values(self):
        return OutputSchemaManager.get_output_schema().values()

    def get(self, key, default=None):
        return OutputSchemaManager.get_output_schema().get(key, default)

OUTPUT_SCHEMA = _LegacySchemaProxy()


class OutputSchemaManager:
    """Manages output schema from configuration files"""

    _config_cache = None
    _schema_cache = None

    @classmethod
    def load_output_columns_config(cls) -> Dict[str, Any]:
        """Load output columns configuration from config file"""
        if cls._config_cache is not None:
            return cls._config_cache

        try:
            # Look for config file in multiple locations
            config_paths = [
                Path("config/output_columns.yaml"),
                Path("../config/output_columns.yaml"),
                Path("../../config/output_columns.yaml")
            ]

            config_path = None
            for path in config_paths:
                if path.exists():
                    config_path = path
                    break

            if not config_path:
                logging.getLogger(__name__).error("output_columns.yaml not found")
                return {}

            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)

            cls._config_cache = config
            logging.getLogger(__name__).debug(f"Loaded output columns config from {config_path}")
            return config

        except Exception as e:
            logging.getLogger(__name__).error(f"Error loading output columns config: {e}")
            return {}

    @classmethod
    def get_output_schema(cls) -> Dict[str, Any]:
        """Get Polars schema from output columns config"""
        if cls._schema_cache is not None:
            return cls._schema_cache

        config = cls.load_output_columns_config()
        if not config or 'columns' not in config:
            # Fallback to minimal schema if config not available
            return {
                'symbol': pl.Utf8,
                'timestamp': pl.Datetime,
                'expiry_date': pl.Date,
                'strike': pl.Float32,
                'expiry_type': pl.Utf8,
                'calculation_timestamp': pl.Datetime
            }

        schema = {}
        for column_name, column_config in config['columns'].items():
            # Support both old format (type) and new format (polars_type)
            polars_type_str = column_config.get('polars_type') or column_config.get('type', 'Utf8')
            try:
                polars_type = getattr(pl, polars_type_str)
                schema[column_name] = polars_type
            except AttributeError:
                logging.getLogger(__name__).warning(f"Unknown Polars type: {polars_type_str}, using Utf8")
                schema[column_name] = pl.Utf8

        cls._schema_cache = schema
        return schema

    @classmethod
    def get_column_order(cls) -> List[str]:
        """Get column order from config (uses order of columns in config file)"""
        config = cls.load_output_columns_config()
        if config and 'columns' in config:
            # Return columns in the order they appear in the config file
            return list(config['columns'].keys())

        # Fallback to schema keys order
        return list(cls.get_output_schema().keys())

    @classmethod
    def get_required_columns(cls) -> List[str]:
        """Get list of required columns"""
        config = cls.load_output_columns_config()
        if not config or 'columns' not in config:
            return ['symbol', 'timestamp', 'expiry_date', 'strike', 'expiry_type']

        required = []
        for column_name, column_config in config['columns'].items():
            if column_config.get('required', False):
                required.append(column_name)

        return required

    @classmethod
    def get_default_value(cls, column_name: str) -> Any:
        """Get default value for a column"""
        config = cls.load_output_columns_config()
        if config and 'columns' in config and column_name in config['columns']:
            return config['columns'][column_name].get('default', 0)
        return 0


class DataValidator:
    """Validate and transform data according to schemas"""

    @staticmethod
    def validate_input_data(df: pl.DataFrame) -> pl.DataFrame:
        """Validate and cast input data to correct types"""
        if df.is_empty():
            return df

        # Check required columns
        required_columns = OutputSchemaManager.get_required_columns()
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")

        # Cast columns to correct types where possible
        for col, dtype in INPUT_SCHEMA.items():
            if col in df.columns:
                try:
                    df = df.with_columns(pl.col(col).cast(dtype, strict=False))
                except Exception as e:
                    # Log warning but continue
                    logger = logging.getLogger(__name__)
                    logger.warning(f"Could not cast column {col} to {dtype}: {e}")

        return df
    
    @staticmethod
    def create_output_dataframe(input_df: pl.DataFrame) -> pl.DataFrame:
        """Create output DataFrame with proper schema from config"""
        output_schema = OutputSchemaManager.get_output_schema()

        if input_df.is_empty():
            return pl.DataFrame(schema=output_schema)

        # Start with key columns from input
        required_columns = OutputSchemaManager.get_required_columns()
        data_columns = ['current_price', 'last_price', 'oi', 'volume', 'delta_volume','delta', 'gamma', 'theta', 'vega', 'iv']

        # Select existing columns
        existing_columns = [col for col in required_columns + data_columns if col in input_df.columns]
        output_df = input_df.select(existing_columns)

        # Add missing columns with default values
        for col, dtype in output_schema.items():
            if col not in output_df.columns:
                default_value = OutputSchemaManager.get_default_value(col)
                if col == 'calculation_timestamp':
                    from datetime import datetime
                    output_df = output_df.with_columns(pl.lit(datetime.now()).alias(col))
                else:
                    output_df = output_df.with_columns(pl.lit(default_value).cast(dtype).alias(col))

        # Ensure calculation timestamp is set
        if 'calculation_timestamp' not in output_df.columns:
            from datetime import datetime
            output_df = output_df.with_columns(pl.lit(datetime.now()).alias('calculation_timestamp'))

        # Order columns according to config
        column_order = OutputSchemaManager.get_column_order()
        available_columns = [col for col in column_order if col in output_df.columns]

        return output_df.select(available_columns)
    
    @staticmethod
    def get_partition_columns() -> List[str]:
        """Get columns used for partitioning data"""
        return ['symbol', 'expiry_date', 'expiry_type', 'strike']
    
    @staticmethod
    def create_partition_key(row: Dict[str, Any]) -> str:
        """Create partition key from row data"""
        partition_cols = DataValidator.get_partition_columns()
        key_parts = []
        
        for col in partition_cols:
            value = row.get(col, 'UNKNOWN')
            if isinstance(value, float):
                value = f"{value:.2f}"
            key_parts.append(str(value))
        
        return "|".join(key_parts)

    @staticmethod
    def ensure_clickhouse_column_order(df: pl.DataFrame, clickhouse_client=None, table_name: str = None) -> pl.DataFrame:
        """
        Ensure DataFrame columns match ClickHouse table order

        Args:
            df: Input DataFrame
            clickhouse_client: ClickHouse client instance (optional)
            table_name: ClickHouse table name (optional)

        Returns:
            DataFrame with columns ordered to match ClickHouse table
        """
        if df.is_empty():
            return df

        # Try to get column order from ClickHouse table if client provided
        if clickhouse_client and table_name:
            try:
                table_column_order = clickhouse_client.get_table_column_order(table_name)
                if table_column_order:
                    # Select columns in ClickHouse table order, only if they exist in DataFrame
                    available_columns = [col for col in table_column_order if col in df.columns]
                    return df.select(available_columns)
            except Exception as e:
                logger = logging.getLogger(__name__)
                logger.warning(f"Could not get ClickHouse column order: {e}")

        # Fallback to config-defined order
        config_column_order = OutputSchemaManager.get_column_order()
        available_columns = [col for col in config_column_order if col in df.columns]

        return df.select(available_columns)
