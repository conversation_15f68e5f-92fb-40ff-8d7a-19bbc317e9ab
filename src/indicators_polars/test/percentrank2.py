import polars as pl

# Input data
values = [
0.0033,0.0036,0.0036,0.0037,0.0037,0.0038,0.0037,0.0037,0.0038,0.0038,0.0038,0.0037,0.004,0.0041,0.0041,0.0041,0.0041,0.0041,0.0042,0.0041,0.0041,0.0041,0.004,0.004,0.0039,0.0039,0.004,0.004,0.0039,0.0038,0.0036,0.0037,0.0037,0.0036,0.0035,0.0035,0.0032,0.003,0.0029,0.003,0.0031,0.003,0.0027,0.0027,0.0027,0.0027,0.0026,0.0024,0.0024,0.0022,0.0024,0.0024,0.0023,0.0022,0.0023,0.0024,0.0023,0.0024,0.0026,0.0026,0.0025,0.0026,0.0027,0.0027,0.0026,0.0025,0.0027,0.0028,0.0026,0.0028,0.0027,0.0026,0.0025,0.0025,0.0025,0.0025,0.0023,0.0025,0.0027,0.0028,0.0029,0.003,0.0027,0.0026,0.0028,0.0026,0.0025,0.0025,0.0024,0.0024,0.0025,0.0025,0.0025,0.0026,0.0025,0.0026,0.0027,0.0025,0.0025,0.0024,0.0024,0.0024,0.0024,0.0024,0.0024,0.0023,0.0023,0.0023,0.0024,0.0026,0.0025,0.0025,0.0024,0.0023,0.0023,0.0023,0.0024,0.0026,0.0026,0.0025,0.0026,0.0026,0.0026,0.0026,0.0026,0.0027,0.0028,0.0029,0.0028,0.0029,0.0028,0.0027,0.0028,0.0026,0.0027,0.0027,0.0027,0.0026,0.0024,0.0024,0.0024,0.0024,0.0024,0.0024,0.0023,0.0024,0.0026,0.0025,0.0025,0.0025,0.0024,0.0023,0.0025,0.0027,0.0025,0.0026,0.0025,0.0025,0.0026,0.0026,0.0026,0.0025,0.0025,0.0027,0.0026,0.0025,0.0027,0.0027,0.0027,0.0026,0.0026,0.0025,0.0026,0.0027,0.0026,0.0028,0.0028,0.0027,0.0028,0.0027,0.0027,0.003,0.0031,0.003,0.0031,0.0031,0.003,0.0029,0.0027,0.0026,0.0024,0.0025,0.0024,0.0024,0.0024,0.0024,0.0024,0.0023,0.0021,0.0022,0.0022,0.0022,0.0021,0.0021,0.002,0.002,0.002,0.002,0.0021,0.0023,0.0023,0.0023,0.0022,0.0022,0.0022,0.0021,0.0022,0.0022,0.0022,0.0022,0.0022,0.0022,0.0022,0.0022,0.0022,0.0022,0.0023,0.0022,0.0022,0.0023,0.0023,0.0024,0.0024,0.0025,0.0024,0.0025,0.0028,0.0026,0.0028,0.0027,0.0028,0.0028,0.0028,0.0035,0.0036,0.0039,0.0041,0.0041,0.0042,0.0045,0.0041,0.0046,0.0046,0.0045,0.0045,0.0042,0.0041,0.0039,0.0037,0.004,0.0038,0.0038,0.004,0.0046,0.0043,0.0053,0.0062,0.0064,0.0063,0.0064,0.0062,0.0063,0.0064,0.0066,0.0066,0.0065,0.0065,0.0066,0.007,0.0062,0.0044,0.0046,0.0046,0.0047,0.0039,0.0037,0.0039,0.0032,0.0026,0.0027,0.0028,0.0026,0.0025,0.0023,0.0028,0.0037,0.0031,0.0024,0.0028,0.0027,0.0029,0.003,0.0029,0.0027,0.0029,0.0037,0.0038,0.0043,0.0035,0.0021,0.0019,0.0017,0.0017,0.0016,0.0018,0.0017,0.0017,0.0025,0.0019,0.0021,0.0026,0.0038,0.0042,0.0035,0.0029,0.0042,0.004,0.0037,0.0041,0.0041,0.0058,0.0055,0.0047,0.0041,0.0043,0.0046,0.0024,0.0019,0.0014,0.0015,0.0021,0.003,0.0021,0.0018,0.0018,0.0016,0.0015,0.0012,0.0011,0.0012,0.0012,0.0012,0.0011,0.0014,0.0013,0.0015,0.0019,0.0015,0.0012,0.0012,0.0012,0.0009,0.0006,0.0006,0.0006,0.0006,0.0006,0.0009,0.0006,0.0006,0.0006,0.0006,0.0006,0.0006

]

df = pl.DataFrame({"Value": values})

# Progressive rank & percent rank
ranks = []
ranks_min = []
ranks_ordinal = []
ranks_max = []
percent_ranks = []

for i in range(df.height):
    # Take the slice up to row i
    subset = df[: i + 1]
    # Rank values in this slice
    ranked = subset.with_columns(
        subset["Value"].rank("average", descending=True).alias("Rank")
    )
    ranked_min = subset.with_columns(
        subset["Value"].rank("min" , descending=True).alias("Rank_min")
    )
    ranked_ordinal = subset.with_columns(
        subset["Value"].rank("ordinal" , descending=True).alias("Rank_ordinal")
    )
    ranked_max = subset.with_columns(
        subset["Value"].rank("max", descending=True).alias("Rank_max")
    )
    # Get the rank of the last row in the slice
    current_rank = ranked[-1, "Rank"]
    current_rank_min = ranked_min[-1, "Rank_min"]
    current_rank_ordinal = ranked_ordinal[-1, "Rank_ordinal"]
    current_rank_max = ranked_max[-1, "Rank_max"]
    ranks.append(current_rank)
    ranks_min.append(current_rank_min)
    ranks_ordinal.append(current_rank_ordinal)
    ranks_max.append(current_rank_max)
    # Percent rank
    if i == 0:
        percent_ranks.append(0.0)
    else:
        percent_ranks.append((current_rank - 1) / i)

# Add results to df
df = df.with_columns([
    pl.Series("Rank", ranks),
    pl.Series("Rank_min", ranks_min),
    pl.Series("Rank_max", ranks_max),
    pl.Series("Rank_ordinal", ranks_ordinal),
    pl.Series("Percent_Rank", percent_ranks)
])
df2= df.to_pandas()
print(df2)
