"""
Live processing mode with delta updates every minute
"""

import logging
import time
from typing import Optional
from datetime import datetime, date
import polars as pl

from .config_manager import ConfigManager
from .indicator_engine import IndicatorEngine
from ..database.clickhouse_client import ClickHouseClient
from ..utils.monitoring import PerformanceMonitor


logger = logging.getLogger(__name__)


class LiveProcessor:
    """Process live data with delta updates"""
    
    def __init__(
        self,
        config_manager: ConfigManager,
        clickhouse_client: ClickHouseClient,
        indicator_engine: IndicatorEngine,
        performance_monitor: PerformanceMonitor
    ):
        self.config = config_manager
        self.db_client = clickhouse_client
        self.indicator_engine = indicator_engine
        self.monitor = performance_monitor
        self.last_processed_timestamp: Optional[datetime] = None
        self.is_running = False
    
    def start_live_processing(self) -> bool:
        """Start live processing loop"""
        logger.info("Starting live processing mode")
        
        # Validate setup
        if not self.validate_live_setup():
            logger.error("Live setup validation failed")
            return False
        
        # Create output table
        output_table = self.config.output.table_name
        if self.config.output.create_table:
            if not self.db_client.create_output_table(output_table):
                logger.error("Failed to create output table")
                return False
        
        # Initialize last processed timestamp
        self._initialize_last_timestamp()
        
        self.is_running = True
        logger.info("Live processing started successfully")
        
        try:
            while self.is_running:
                # Check if we're in market hours
                current_time = datetime.now(self.config.get_timezone())
                
                if not self.config.is_market_time(current_time):
                    logger.debug("Outside market hours, sleeping...")
                    time.sleep(60)  # Sleep for 1 minute
                    continue
                
                # Process delta data
                success = self.process_delta_update()
                
                if not success:
                    logger.warning("Delta update failed, continuing...")
                
                # Sleep until next polling interval
                time.sleep(self.config.processing.polling_interval_seconds)
                
        except KeyboardInterrupt:
            logger.info("Live processing interrupted by user")
        except Exception as e:
            logger.error(f"Unexpected error in live processing: {e}")
            return False
        finally:
            self.is_running = False
            logger.info("Live processing stopped")
        
        return True
    
    def stop_live_processing(self):
        """Stop live processing"""
        logger.info("Stopping live processing...")
        self.is_running = False
    
    def process_delta_update(self) -> bool:
        """Process a single delta update"""
        current_date = datetime.now(self.config.get_timezone()).date()
        
        with self.monitor.track_operation("delta_update", 0) as metrics:
            try:
                # Fetch delta data
                delta_data = self.db_client.get_data_for_date(
                    target_date=current_date,
                    symbols=self.config.market.symbols,
                    start_time=self.config.market.start_time,
                    end_time=self.config.market.end_time,
                    delta_only=True,
                    last_timestamp=self.last_processed_timestamp
                )
                
                if delta_data.is_empty():
                    logger.debug("No new data found")
                    return True
                
                logger.info(f"Processing {len(delta_data)} new rows")
                metrics.rows_processed = len(delta_data)
                
                # Get existing data for context (needed for proper indicator calculation)
                existing_data = self._get_existing_context_data(current_date)
                
                # Calculate indicators for delta data only
                result_df = self.indicator_engine.calculate_indicators(
                    delta_data,
                    delta_only=True,
                    existing_data=existing_data
                )
                
                if result_df.is_empty():
                    logger.warning("No indicators calculated for delta data")
                    return False
                
                # Save results
                output_table = self.config.output.table_name
                success = self.db_client.insert_indicators_data(
                    result_df,
                    output_table,
                    self.config.output.batch_insert_size
                )
                
                if success:
                    # Update last processed timestamp
                    self._update_last_timestamp(delta_data)
                    logger.info(f"Delta update completed: {len(result_df)} rows processed")
                    return True
                else:
                    logger.error("Failed to save delta update results")
                    return False
                
            except Exception as e:
                logger.error(f"Error in delta update: {e}")
                metrics.errors_count += 1
                return False
    
    def _initialize_last_timestamp(self):
        """Initialize the last processed timestamp"""
        current_date = datetime.now(self.config.get_timezone()).date()
        
        # Try to get the latest timestamp from output table
        try:
            output_table = self.config.output.table_name
            # This would need to be implemented in ClickHouseClient
            # For now, we'll start from the beginning of the day
            market_start = datetime.strptime(self.config.market.start_time, '%H:%M').time()
            tz = self.config.get_timezone()
            self.last_processed_timestamp = tz.localize(
                datetime.combine(current_date, market_start)
            )
            
            logger.info(f"Initialized last processed timestamp: {self.last_processed_timestamp}")
            
        except Exception as e:
            logger.warning(f"Could not initialize last timestamp: {e}")
            self.last_processed_timestamp = None
    
    def _update_last_timestamp(self, processed_data: pl.DataFrame):
        """Update the last processed timestamp based on processed data"""
        if not processed_data.is_empty():
            max_timestamp = processed_data.select(pl.col("timestamp").max()).item()
            if max_timestamp:
                self.last_processed_timestamp = max_timestamp
                logger.debug(f"Updated last processed timestamp: {self.last_processed_timestamp}")
    
    def _get_existing_context_data(self, current_date: date) -> Optional[pl.DataFrame]:
        """
        Get existing data for context in delta calculations
        
        This is needed because some indicators require historical context
        to calculate properly (e.g., moving averages, rankings)
        """
        try:
            # Get data from start of day to last processed timestamp
            if self.last_processed_timestamp is None:
                return None
            
            context_data = self.db_client.get_data_for_date(
                target_date=current_date,
                symbols=self.config.market.symbols,
                start_time=self.config.market.start_time,
                end_time=self.last_processed_timestamp.strftime('%H:%M'),
                delta_only=False
            )
            
            if not context_data.is_empty():
                logger.debug(f"Retrieved {len(context_data)} rows of context data")
                return context_data
            
        except Exception as e:
            logger.warning(f"Could not retrieve context data: {e}")
        
        return None
    
    def validate_live_setup(self) -> bool:
        """Validate that live processing can be started"""
        try:
            current_date = datetime.now(self.config.get_timezone()).date()
            
            # Check if source table exists
            table_name = self.db_client.get_table_name(current_date)
            if not self.db_client.table_exists(table_name):
                logger.error(f"Source table {table_name} does not exist")
                return False
            
            # Check if we can connect to database
            latest_timestamp = self.db_client.get_latest_timestamp(
                current_date, 
                self.config.market.symbols
            )
            
            if latest_timestamp is None:
                logger.warning(f"No data found in {table_name}")
                # This might be okay if it's early in the day
            
            # Validate configuration
            if not self.config.validate():
                logger.error("Configuration validation failed")
                return False
            
            # Check processing time constraint
            if self.config.processing.max_processing_time_seconds >= 60:
                logger.warning(
                    f"Max processing time ({self.config.processing.max_processing_time_seconds}s) "
                    "should be less than 60s for live mode"
                )
            
            logger.info("Live setup validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Live setup validation failed: {e}")
            return False
    
    def get_processing_status(self) -> dict:
        """Get current processing status"""
        return {
            'is_running': self.is_running,
            'last_processed_timestamp': self.last_processed_timestamp.isoformat() if self.last_processed_timestamp else None,
            'current_time': datetime.now(self.config.get_timezone()).isoformat(),
            'is_market_time': self.config.is_market_time(datetime.now(self.config.get_timezone())),
            'performance_metrics': self.monitor.get_recent_metrics(5)
        }
    
    def force_delta_update(self) -> bool:
        """Force a delta update (useful for testing or manual triggers)"""
        logger.info("Forcing delta update")
        return self.process_delta_update()
    
    def reset_last_timestamp(self):
        """Reset last processed timestamp (useful for reprocessing)"""
        logger.warning("Resetting last processed timestamp")
        self.last_processed_timestamp = None
        self._initialize_last_timestamp()
