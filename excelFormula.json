{"symbol": "RAW / Direct Value", "timestamp": "RAW / Direct Value", "expiry_date": "RAW / Direct Value", "current_price": "RAW / Direct Value", "strike": "RAW / Direct Value", "strike_type": "RAW / Direct Value", "lot_size": "RAW / Direct Value", "expiry_type": "RAW / Direct Value", "moneyness": "RAW / Direct Value", "last_price": "RAW / Direct Value", "ltp_change": "RAW / Direct Value", "oi": "RAW / Direct Value", "oi_change": "RAW / Direct Value", "volume": "RAW / Direct Value", "is_liquid": "RAW / Direct Value", "theta": "RAW / Direct Value", "delta": "RAW / Direct Value", "gamma": "RAW / Direct Value", "vega": "RAW / Direct Value", "iv": "RAW / Direct Value", "batch": "RAW / Direct Value", "prev_current_price": "RAW / Direct Value", "prev_last_price": "RAW / Direct Value", "prev_oi": "RAW / Direct Value", "prev_volume": "RAW / Direct Value", "prev_delta": "RAW / Direct Value", "prev_gamma": "RAW / Direct Value", "prev_iv": "RAW / Direct Value", "delta_current_price": "RAW / Direct Value", "delta_last_price": "RAW / Direct Value", "delta_oi": "RAW / Direct Value", "delta_volume": "RAW / Direct Value", "delta_delta": "RAW / Direct Value", "delta_gamma": "RAW / Direct Value", "delta_iv": "RAW / Direct Value", "OI Rank": ["=IFERROR(PERCENTRANK(L$2:$L4,L4),0)", "=IFERROR(PERCENTRANK(L$2:$L5,L5),0)", "=IFERROR(PERCENTRANK(L$2:$L6,L6),0)", "=IFERROR(PERCENTRANK(L$2:$L7,L7),0)", "=IFERROR(PERCENTRANK(L$2:$L8,L8),0)"], "%": ["=(BE3*100)", "=(BE4*100)", "=(BE5*100)", "=(BE6*100)", "=(BE7*100)"], "1 CANDLE DIP": ["=IF(AL4<Master!$T$2,\"YES\",\"\")", "=IF(AL5<Master!$T$2,\"YES\",\"\")", "=IF(AL6<Master!$T$2,\"YES\",\"\")", "=IF(AL7<Master!$T$2,\"YES\",\"\")", "=IF(AL8<Master!$T$2,\"YES\",\"\")"], "INT VALUE": ["=IF(H2=\"CE\",IF(E2<D2,ABS(D2-E2),\"\"),IF(E2>D2,ABS(E2-D2),\"\"))", "=IF(H3=\"CE\",IF(E3<D3,ABS(D3-E3),\"\"),IF(E3>D3,ABS(E3-D3),\"\"))", "=IF(H4=\"CE\",IF(E4<D4,ABS(D4-E4),\"\"),IF(E4>D4,ABS(E4-D4),\"\"))", "=IF(H5=\"CE\",IF(E5<D5,ABS(D5-E5),\"\"),IF(E5>D5,ABS(E5-D5),\"\"))", "=IF(H6=\"CE\",IF(E6<D6,ABS(D6-E6),\"\"),IF(E6>D6,ABS(E6-D6),\"\"))"], "PREM": ["=PERCENTRANK($AN$2:AN3,AN3)", "=PERCENTRANK($AN$2:AN4,AN4)", "=PERCENTRANK($AN$2:AN5,AN5)", "=PERCENTRANK($AN$2:AN6,AN6)", "=PERCENTRANK($AN$2:AN7,AN7)"], "Consecutive Price Drop": ["=IF(AND(AD3<0,AD2<0),\"Yes\",\"\")", "=IF(AND(AD4<0,AD3<0),\"Yes\",\"\")", "=IF(AND(AD5<0,AD4<0),\"Yes\",\"\")", "=IF(AND(AD6<0,AD5<0),\"Yes\",\"\")", "=IF(AND(AD7<0,AD6<0),\"Yes\",\"\")"], "Consecutive OI Build": ["=IF(AND(AE3>=0,AE2>=0),\"Yes\",\"\")", "=IF(AND(AE4>=0,AE3>=0),\"Yes\",\"\")", "=IF(AND(AE5>=0,AE4>=0),\"Yes\",\"\")", "=IF(AND(AE6>=0,AE5>=0),\"Yes\",\"\")", "=IF(AND(AE7>=0,AE6>=0),\"Yes\",\"\")"], "INT VALUE RANK": ["=IFERROR(PERCENTRANK(AM$2:$AM3,AM3),0)", "=IFERROR(PERCENTRANK(AM$2:$AM4,AM4),0)", "=IFERROR(PERCENTRANK(AM$2:$AM5,AM5),0)", "=IFERROR(PERCENTRANK(AM$2:$AM6,AM6),0)", "=IFERROR(PERCENTRANK(AM$2:$AM7,AM7),0)"], "GAMMA RANK": ["=PERCENTRANK($R$2:R3,R2)", "=PERCENTRANK($R$2:R4,R4)", "=PERCENTRANK($R$2:R5,R5)", "=PERCENTRANK($R$2:R6,R6)", "=PERCENTRANK($R$2:R7,R7)"], "DEL VOLRANK": ["=PERCENTRANK($AF$2:AF3,AF2)", "=PERCENTRANK($AF$2:AF4,AF4)", "=PERCENTRANK($AF$2:AF5,AF5)", "=PERCENTRANK($AF$2:AF6,AF6)", "=PERCENTRANK($AF$2:AF7,AF7)"], "IVRANK": ["=PERCENTRANK($T$2:T3,T2)", "=PERCENTRANK($T$2:T4,T4)", "=PERCENTRANK($T$2:T5,T5)", "=PERCENTRANK($T$2:T6,T6)", "=PERCENTRANK($T$2:T7,T7)"], "VEGA RANK": ["=PERCENTRANK($S$2:S3,S2)", "=PERCENTRANK($S$2:S4,S4)", "=PERCENTRANK($S$2:S5,S5)", "=PERCENTRANK($S$2:S6,S6)", "=PERCENTRANK($S$2:S7,S7)"], "DELTARANK": ["=PERCENTRANK($Q$2:Q3,Q3)", "=PERCENTRANK($Q$2:Q4,Q4)", "=PERCENTRANK($Q$2:Q5,Q5)", "=PERCENTRANK($Q$2:Q6,Q6)", "=PERCENTRANK($Q$2:Q7,Q7)"], "THETATANK": ["=PERCENTRANK($P$2:P3,P3)", "=PERCENTRANK($P$2:P4,P4)", "=PERCENTRANK($P$2:P5,P5)", "=PERCENTRANK($P$2:P6,P6)", "=PERCENTRANK($P$2:P7,P7)"], "1 CANDLE RISE": ["=IF(AL4>Master!$U$2,\"YES\",\"\")", "=IF(AL5>Master!$U$2,\"YES\",\"\")", "=IF(AL6>Master!$U$2,\"YES\",\"\")", "=IF(AL7>Master!$U$2,\"YES\",\"\")", "=IF(AL8>Master!$U$2,\"YES\",\"\")"], "INTS VALUE RISE": ["=IF(AND(AR4>Master!$G$2,AR3>Master!$G$3,AR2>Master!$G$4),\"YES\",\"\")", "=IF(AND(AR5>Master!$G$2,AR4>Master!$G$3,AR3>Master!$G$4),\"YES\",\"\")", "=IF(AND(AR6>Master!$G$2,AR5>Master!$G$3,AR4>Master!$G$4),\"YES\",\"\")", "=IF(AND(AR7>Master!$G$2,AR6>Master!$G$3,AR5>Master!$G$4),\"YES\",\"\")", "=IF(AND(AR8>Master!$G$2,AR7>Master!$G$3,AR6>Master!$G$4),\"YES\",\"\")"], "INTS VALUE DROP": ["=IF(AND(AR4<Master!$H$2,AR3<Master!$H$3,AR2<Master!$H$4),\"YES\",\"\")", "=IF(AND(AR5<Master!$H$2,AR4<Master!$H$3,AR3<Master!$H$4),\"YES\",\"\")", "=IF(AND(AR6<Master!$H$2,AR5<Master!$H$3,AR4<Master!$H$4),\"YES\",\"\")", "=IF(AND(AR7<Master!$H$2,AR6<Master!$H$3,AR5<Master!$H$4),\"YES\",\"\")", "=IF(AND(AR8<Master!$H$2,AR7<Master!$H$3,AR6<Master!$H$4),\"YES\",\"\")"], "PREMIUM RISE": ["=IF(AND(AT4>Master!$I$2,AT3>Master!$I$3,AT2>Master!$I$4),\"YES\",\"\")", "=IF(AND(AT5>Master!$I$2,AT4>Master!$I$3,AT3>Master!$I$4),\"YES\",\"\")", "=IF(AND(AT6>Master!$I$2,AT5>Master!$I$3,AT4>Master!$I$4),\"YES\",\"\")", "=IF(AND(AT7>Master!$I$2,AT6>Master!$I$3,AT5>Master!$I$4),\"YES\",\"\")", "=IF(AND(AT8>Master!$I$2,AT7>Master!$I$3,AT6>Master!$I$4),\"YES\",\"\")"], "PREMIUM DROP": ["=IF(AND(AT4<Master!$J$2,AT3<Master!$J$3,AT2<Master!$J$4),\"YES\",\"\")", "=IF(AND(AT5<Master!$J$2,AT4<Master!$J$3,AT3<Master!$J$4),\"YES\",\"\")", "=IF(AND(AT6<Master!$J$2,AT5<Master!$J$3,AT4<Master!$J$4),\"YES\",\"\")", "=IF(AND(AT7<Master!$J$2,AT6<Master!$J$3,AT5<Master!$J$4),\"YES\",\"\")", "=IF(AND(AT8<Master!$J$2,AT7<Master!$J$3,AT6<Master!$J$4),\"YES\",\"\")"], "GAMMA RISE": ["=IF(AND(AV4>Master!$K$2,AV3>Master!$K$3,AV2>Master!$K$4,AV2<Master!$K$2),\"YES\",\"\")", "=IF(AND(AV5>Master!$K$2,AV4>Master!$K$3,AV3>Master!$K$4,AV3<Master!$K$2),\"YES\",\"\")", "=IF(AND(AV6>Master!$K$2,AV5>Master!$K$3,AV4>Master!$K$4,AV4<Master!$K$2),\"YES\",\"\")", "=IF(AND(AV7>Master!$K$2,AV6>Master!$K$3,AV5>Master!$K$4,AV5<Master!$K$2),\"YES\",\"\")", "=IF(AND(AV8>Master!$K$2,AV7>Master!$K$3,AV6>Master!$K$4,AV6<Master!$K$2),\"YES\",\"\")"], "DEL VOL RISE": ["=IF(AND(AX4>Master!$L$2,AX3>Master!$L$3,AX2>Master!$L$4),\"YES\",\"\")", "=IF(AND(AX5>Master!$L$2,AX4>Master!$L$3,AX3>Master!$L$4),\"YES\",\"\")", "=IF(AND(AX6>Master!$L$2,AX5>Master!$L$3,AX4>Master!$L$4),\"YES\",\"\")", "=IF(AND(AX7>Master!$L$2,AX6>Master!$L$3,AX5>Master!$L$4),\"YES\",\"\")", "=IF(AND(AX8>Master!$L$2,AX7>Master!$L$3,AX6>Master!$L$4),\"YES\",\"\")"], "IV Double RISE": ["=IF(AND(AZ4>Master!$M$2,AZ3>Master!$M$3),\"YES\",\"\")", "=IF(AND(AZ5>Master!$M$2,AZ4>Master!$M$3),\"YES\",\"\")", "=IF(AND(AZ6>Master!$M$2,AZ5>Master!$M$3),\"YES\",\"\")", "=IF(AND(AZ7>Master!$M$2,AZ6>Master!$M$3),\"YES\",\"\")", "=IF(AND(AZ8>Master!$M$2,AZ7>Master!$M$3),\"YES\",\"\")"], "IV Tripple RISE": ["=IF(AND(AZ4>Master!$M$2,AZ3>Master!$M$3,AZ2>Master!$M$4,AZ2<Master!$M$2),\"YES\",\"\")", "=IF(AND(AZ5>Master!$M$2,AZ4>Master!$M$3,AZ3>Master!$M$4,AZ3<Master!$M$2),\"YES\",\"\")", "=IF(AND(AZ6>Master!$M$2,AZ5>Master!$M$3,AZ4>Master!$M$4,AZ4<Master!$M$2),\"YES\",\"\")", "=IF(AND(AZ7>Master!$M$2,AZ6>Master!$M$3,AZ5>Master!$M$4,AZ5<Master!$M$2),\"YES\",\"\")", "=IF(AND(AZ8>Master!$M$2,AZ7>Master!$M$3,AZ6>Master!$M$4,AZ6<Master!$M$2),\"YES\",\"\")"], "IV DROP": ["=IF(AND(AZ4<Master!$N$2,AZ3<Master!$N$3,AZ2<Master!$N$4,AZ2>Master!$N$2),\"YES\",\"\")", "=IF(AND(AZ5<Master!$N$2,AZ4<Master!$N$3,AZ3<Master!$N$4,AZ3>Master!$N$2),\"YES\",\"\")", "=IF(AND(AZ6<Master!$N$2,AZ5<Master!$N$3,AZ4<Master!$N$4,AZ4>Master!$N$2),\"YES\",\"\")", "=IF(AND(AZ7<Master!$N$2,AZ6<Master!$N$3,AZ5<Master!$N$4,AZ5>Master!$N$2),\"YES\",\"\")", "=IF(AND(AZ8<Master!$N$2,AZ7<Master!$N$3,AZ6<Master!$N$4,AZ6>Master!$N$2),\"YES\",\"\")"], "DELTA RISE": ["=IF(AND(BD4>Master!$O$2,BD3>Master!$O$3,BD2>Master!$O$4),\"YES\",\"\")", "=IF(AND(BD5>Master!$O$2,BD4>Master!$O$3,BD3>Master!$O$4),\"YES\",\"\")", "=IF(AND(BD6>Master!$O$2,BD5>Master!$O$3,BD4>Master!$O$4),\"YES\",\"\")", "=IF(AND(BD7>Master!$O$2,BD6>Master!$O$3,BD5>Master!$O$4),\"YES\",\"\")", "=IF(AND(BD8>Master!$O$2,BD7>Master!$O$3,BD6>Master!$O$4),\"YES\",\"\")"], "DELTA DROP": ["=IF(AND(BD4<Master!$P$2,BD3<Master!$P$3,BD2<Master!$P$4),\"YES\",\"\")", "=IF(AND(BD5<Master!$P$2,BD4<Master!$P$3,BD3<Master!$P$4),\"YES\",\"\")", "=IF(AND(BD6<Master!$P$2,BD5<Master!$P$3,BD4<Master!$P$4),\"YES\",\"\")", "=IF(AND(BD7<Master!$P$2,BD6<Master!$P$3,BD5<Master!$P$4),\"YES\",\"\")", "=IF(AND(BD8<Master!$P$2,BD7<Master!$P$3,BD6<Master!$P$4),\"YES\",\"\")"], "THETA RISE": ["=IF(AND(BF4>Master!$Q$2,BF3>Master!$Q$3,BF2>Master!$Q$4),\"YES\",\"\")", "=IF(AND(BF5>Master!$Q$2,BF4>Master!$Q$3,BF3>Master!$Q$4),\"YES\",\"\")", "=IF(AND(BF6>Master!$Q$2,BF5>Master!$Q$3,BF4>Master!$Q$4),\"YES\",\"\")", "=IF(AND(BF7>Master!$Q$2,BF6>Master!$Q$3,BF5>Master!$Q$4),\"YES\",\"\")", "=IF(AND(BF8>Master!$Q$2,BF7>Master!$Q$3,BF6>Master!$Q$4),\"YES\",\"\")"], "THETA DROP": ["=IF(AND(BF4<Master!$R$2,BF3<Master!$R$3,BF2<Master!$R$4),\"YES\",\"\")", "=IF(AND(BF5<Master!$R$2,BF4<Master!$R$3,BF3<Master!$R$4),\"YES\",\"\")", "=IF(AND(BF6<Master!$R$2,BF5<Master!$R$3,BF4<Master!$R$4),\"YES\",\"\")", "=IF(AND(BF7<Master!$R$2,BF6<Master!$R$3,BF5<Master!$R$4),\"YES\",\"\")", "=IF(AND(BF8<Master!$R$2,BF7<Master!$R$3,BF6<Master!$R$4),\"YES\",\"\")"], "OI Rise": ["=IF(AND(AK4>Master!$V$2,AK3>Master!$V$3,AK2>Master!$V$4),\"YES\",\"\")", "=IF(AND(AK5>Master!$V$2,AK4>Master!$V$3,AK3>Master!$V$4),\"YES\",\"\")", "=IF(AND(AK6>Master!$V$2,AK5>Master!$V$3,AK4>Master!$V$4),\"YES\",\"\")", "=IF(AND(AK7>Master!$V$2,AK6>Master!$V$3,AK5>Master!$V$4),\"YES\",\"\")", "=IF(AND(AK8>Master!$V$2,AK7>Master!$V$3,AK6>Master!$V$4),\"YES\",\"\")"], "VEGA COOL": ["=IF(AND(BB4<=Master!$S$2,BB3<Master!$S$3,BB2<Master!$S$4,BB2>Master!$S$2),\"YES\",\"\")", "=IF(AND(BB5<=Master!$S$2,BB4<Master!$S$3,BB3<Master!$S$4),\"YES\",\"\")", "=IF(AND(BB6<=Master!$S$2,BB5<Master!$S$3,BB4<Master!$S$4),\"YES\",\"\")", "=IF(AND(BB7<=Master!$S$2,BB6<Master!$S$3,BB5<Master!$S$4),\"YES\",\"\")", "=IF(AND(BB8<=Master!$S$2,BB7<Master!$S$3,BB6<Master!$S$4),\"YES\",\"\")"], "(GAMMA MOVE)": ["=IF(BM4=\"YES\",\"GAMMA MOVE\",\"\")", "=IF(BM5=\"YES\",\"GAMMA MOVE\",\"\")", "=IF(BM6=\"YES\",\"GAMMA MOVE\",\"\")", "=IF(BM7=\"YES\",\"GAMMA MOVE\",\"\")", "=IF(BM8=\"YES\",\"GAMMA MOVE\",\"\")"], "BUY 1 (6 check)\nRISE: INS VALUE, VOL, IV, DELTA\nDROP: PREM": ["=IF(AND(BI4=\"YES\",BL4=\"YES\",BN4=\"YES\",BR4=\"YES\",BP4=\"YES\"),\"BUY1\",\"\")", "=IF(AND(BI5=\"YES\",BL5=\"YES\",BN5=\"YES\",BR5=\"YES\",BP5=\"YES\"),\"BUY1\",\"\")", "=IF(AND(BI6=\"YES\",BL6=\"YES\",BN6=\"YES\",BR6=\"YES\",BP6=\"YES\"),\"BUY1\",\"\")", "=IF(AND(BI7=\"YES\",BL7=\"YES\",BN7=\"YES\",BR7=\"YES\",BP7=\"YES\"),\"BUY1\",\"\")", "=IF(AND(BI8=\"YES\",BL8=\"YES\",BN8=\"YES\",BR8=\"YES\",BP8=\"YES\"),\"BUY1\",\"\")"], "BUY 2 (5 check)\nRISE: INS VALUE,  IV, DELTA\nDROP: PREM, VOL": ["=IF(AND(BI4=\"YES\",BL4=\"YES\",BR4=\"YES\",BP4=\"YES\",BN4=\"\"),\"BUY2\",\"\")", "=IF(AND(BI5=\"YES\",BL5=\"YES\",BR5=\"YES\",BP5=\"YES\",BN5=\"\"),\"BUY2\",\"\")", "=IF(AND(BI6=\"YES\",BL6=\"YES\",BR6=\"YES\",BP6=\"YES\",BN6=\"\"),\"BUY2\",\"\")", "=IF(AND(BI7=\"YES\",BL7=\"YES\",BR7=\"YES\",BP7=\"YES\",BN7=\"\"),\"BUY2\",\"\")", "=IF(AND(BI8=\"YES\",BL8=\"YES\",BR8=\"YES\",BP8=\"YES\",BN8=\"\"),\"BUY2\",\"\")"], "BUY 3 (5 check)\nRISE: INS VALUE, VOL, DELTA\nDROP: PREM, IV": ["=IF(AND(BI4=\"YES\",BL4=\"YES\",BN4=\"YES\",BR4=\"YES\",BP4=\"\"),\"BUY3\",\"\")", "=IF(AND(BI5=\"YES\",BL5=\"YES\",BN5=\"YES\",BR5=\"YES\",BP5=\"\"),\"BUY3\",\"\")", "=IF(AND(BI6=\"YES\",BL6=\"YES\",BN6=\"YES\",BR6=\"YES\",BP6=\"\"),\"BUY3\",\"\")", "=IF(AND(BI7=\"YES\",BL7=\"YES\",BN7=\"YES\",BR7=\"YES\",BP7=\"\"),\"BUY3\",\"\")", "=IF(AND(BI8=\"YES\",BL8=\"YES\",BN8=\"YES\",BR8=\"YES\",BP8=\"\"),\"BUY3\",\"\")"], "Buy 4": ["=IF(AND(BI4=\"YES\",BL4=\"YES\",BN4=\"\",BR4=\"YES\",BP4=\"\"),\"BUY4\",\"\")", "=IF(AND(BI5=\"YES\",BL5=\"YES\",BN5=\"\",BR5=\"YES\",BP5=\"\"),\"BUY4\",\"\")", "=IF(AND(BI6=\"YES\",BL6=\"YES\",BN6=\"\",BR6=\"YES\",BP6=\"\"),\"BUY4\",\"\")", "=IF(AND(BI7=\"YES\",BL7=\"YES\",BN7=\"\",BR7=\"YES\",BP7=\"\"),\"BUY4\",\"\")", "=IF(AND(BI8=\"YES\",BL8=\"YES\",BN8=\"\",BR8=\"YES\",BP8=\"\"),\"BUY4\",\"\")"], "BUY 4\n(INS VALUE RISE + PREM RISE": ["=IF(AND(BI4=\"YES\",BK4=\"YES\",BN4=\"YES\",BR4=\"\",BP4=\"\"),\"BUY5\",\"\")", "=IF(AND(BI5=\"YES\",BK5=\"YES\",BN5=\"YES\",BR5=\"\",BP5=\"\"),\"BUY5\",\"\")", "=IF(AND(BI6=\"YES\",BK6=\"YES\",BN6=\"YES\",BR6=\"\",BP6=\"\"),\"BUY5\",\"\")", "=IF(AND(BI7=\"YES\",BK7=\"YES\",BN7=\"YES\",BR7=\"\",BP7=\"\"),\"BUY5\",\"\")", "=IF(AND(BI8=\"YES\",BK8=\"YES\",BN8=\"YES\",BR8=\"\",BP8=\"\"),\"BUY5\",\"\")"], "BUY with IV+VEGA": ["=IF(AND(BP3=\"YES\",BW3=\"YES\"),\"IV UP+VEGA COOL\",IF(AND(BP3=\"\",BW3=\"YES\"),\"VEGA COOL only\",IF(AND(BW3=\"YES\",AZ3>=90),\"IV SPIKE + VEGA cooled\",\"\")))", "=IF(AND(BP4=\"YES\",BW4=\"YES\"),\"IV UP+VEGA COOL\",IF(AND(BP4=\"\",BW4=\"YES\"),\"VEGA COOL only\",IF(AND(BW4=\"YES\",AZ4>=90),\"IV SPIKE + VEGA cooled\",\"\")))", "=IF(AND(BP5=\"YES\",BW5=\"YES\"),\"IV UP+VEGA COOL\",IF(AND(BP5=\"\",BW5=\"YES\"),\"VEGA COOL only\",IF(AND(BW5=\"YES\",AZ5>=90),\"IV SPIKE + VEGA cooled\",\"\")))", "=IF(AND(BP6=\"YES\",BW6=\"YES\"),\"IV UP+VEGA COOL\",IF(AND(BP6=\"\",BW6=\"YES\"),\"VEGA COOL only\",IF(AND(BW6=\"YES\",AZ6>=90),\"IV SPIKE + VEGA cooled\",\"\")))", "=IF(AND(BP7=\"YES\",BW7=\"YES\"),\"IV UP+VEGA COOL\",IF(AND(BP7=\"\",BW7=\"YES\"),\"VEGA COOL only\",IF(AND(BW7=\"YES\",AZ7>=90),\"IV SPIKE + VEGA cooled\",\"\")))"], "BUY with GAMMA": ["=IF(AND(BX4=\"GAMMA MOVE\",OR(BG4=\"YES\",BH4=\"YES\")),\"GAMMA + PA\",IF(AND(BX4=\"GAMMA MOVE\",OR(BG4=\"\",BH4=\"\")),\"GAMMA ONLY\",\"\"))", "=IF(AND(BX5=\"GAMMA MOVE\",OR(BG5=\"YES\",BH5=\"YES\")),\"GAMMA + PA\",IF(AND(BX5=\"GAMMA MOVE\",OR(BG5=\"\",BH5=\"\")),\"GAMMA ONLY\",\"\"))", "=IF(AND(BX6=\"GAMMA MOVE\",OR(BG6=\"YES\",BH6=\"YES\")),\"GAMMA + PA\",IF(AND(BX6=\"GAMMA MOVE\",OR(BG6=\"\",BH6=\"\")),\"GAMMA ONLY\",\"\"))", "=IF(AND(BX7=\"GAMMA MOVE\",OR(BG7=\"YES\",BH7=\"YES\")),\"GAMMA + PA\",IF(AND(BX7=\"GAMMA MOVE\",OR(BG7=\"\",BH7=\"\")),\"GAMMA ONLY\",\"\"))", "=IF(AND(BX8=\"GAMMA MOVE\",OR(BG8=\"YES\",BH8=\"YES\")),\"GAMMA + PA\",IF(AND(BX8=\"GAMMA MOVE\",OR(BG8=\"\",BH8=\"\")),\"GAMMA ONLY\",\"\"))"], "STANDARD BUY COUNT": ["=IF(COUNTA(BY4:CC4)-COUNTBLANK(BY4:CC4)>=1,(COUNTA(BY4:CC4)-COUNTBLANK(BY4:CC4)+CF3),0)", "=IF(COUNTA(BY5:CC5)-COUNTBLANK(BY5:CC5)>=1,(COUNTA(BY5:CC5)-COUNTBLANK(BY5:CC5)+CF4),0)", "=IF(COUNTA(BY6:CC6)-COUNTBLANK(BY6:CC6)>=1,(COUNTA(BY6:CC6)-COUNTBLANK(BY6:CC6)+CF5),0)", "=IF(COUNTA(BY7:CC7)-COUNTBLANK(BY7:CC7)>=1,(COUNTA(BY7:CC7)-COUNTBLANK(BY7:CC7)+CF6),0)", "=IF(COUNTA(BY8:CC8)-COUNTBLANK(BY8:CC8)>=1,(COUNTA(BY8:CC8)-COUNTBLANK(BY8:CC8)+CF7),0)"], "SPECIAL BUY COUNT": ["=IF(COUNTA(CD4:CE4)-COUNTBLANK(CD4:CE4)>=1,(COUNTA(CD4:CE4)-COUNTBLANK(CD4:CE4)+CG3),0)", "=IF(COUNTA(CD5:CE5)-COUNTBLANK(CD5:CE5)>=1,(COUNTA(CD5:CE5)-COUNTBLANK(CD5:CE5)+CG4),0)", "=IF(COUNTA(CD6:CE6)-COUNTBLANK(CD6:CE6)>=1,(COUNTA(CD6:CE6)-COUNTBLANK(CD6:CE6)+CG5),0)", "=IF(COUNTA(CD7:CE7)-COUNTBLANK(CD7:CE7)>=1,(COUNTA(CD7:CE7)-COUNTBLANK(CD7:CE7)+CG6),0)", "=IF(COUNTA(CD8:CE8)-COUNTBLANK(CD8:CE8)>=1,(COUNTA(CD8:CE8)-COUNTBLANK(CD8:CE8)+CG7),0)"], "SELL 1 (6 check)\nRISE: INS VALUE, VOL, IV, DELTA\nDROP: PREM": ["=IF(AND(BJ4=\"YES\",BK4=\"YES\",BN4=\"YES\",BS4=\"YES\",BQ4=\"YES\",BV4=\"YES\"),1,\"\")", "=IF(AND(BJ5=\"YES\",BK5=\"YES\",BN5=\"YES\",BS5=\"YES\",BQ5=\"YES\",BV5=\"YES\"),1,\"\")", "=IF(AND(BJ6=\"YES\",BK6=\"YES\",BN6=\"YES\",BS6=\"YES\",BQ6=\"YES\",BV6=\"YES\"),1,\"\")", "=IF(AND(BJ7=\"YES\",BK7=\"YES\",BN7=\"YES\",BS7=\"YES\",BQ7=\"YES\",BV7=\"YES\"),1,\"\")", "=IF(AND(BJ8=\"YES\",BK8=\"YES\",BN8=\"YES\",BS8=\"YES\",BQ8=\"YES\",BV8=\"YES\"),1,\"\")"], "SELL 2 (5 check)\nRISE: INS VALUE,  IV, DELTA\nDROP: PREM": ["=IF(AND(BJ4=\"YES\",BK4=\"YES\",BN4=\"\",BS4=\"YES\",BQ4=\"YES\",BV4=\"YES\"),2,\"\")", "=IF(AND(BJ5=\"YES\",BK5=\"YES\",BN5=\"\",BS5=\"YES\",BQ5=\"YES\",BV5=\"YES\"),2,\"\")", "=IF(AND(BJ6=\"YES\",BK6=\"YES\",BN6=\"\",BS6=\"YES\",BQ6=\"YES\",BV6=\"YES\"),2,\"\")", "=IF(AND(BJ7=\"YES\",BK7=\"YES\",BN7=\"\",BS7=\"YES\",BQ7=\"YES\",BV7=\"YES\"),2,\"\")", "=IF(AND(BJ8=\"YES\",BK8=\"YES\",BN8=\"\",BS8=\"YES\",BQ8=\"YES\",BV8=\"YES\"),2,\"\")"], "SELL 3 (5 check)\nRISE: INS VALUE,  IV, DELTA\nDROP: PREM": ["=IF(AND(BJ4=\"YES\",BK4=\"YES\",BN4=\"YES\",BS4=\"YES\",BQ4=\"\",BV4=\"YES\"),3,\"\")", "=IF(AND(BJ5=\"YES\",BK5=\"YES\",BN5=\"YES\",BS5=\"YES\",BQ5=\"\",BV5=\"YES\"),3,\"\")", "=IF(AND(BJ6=\"YES\",BK6=\"YES\",BN6=\"YES\",BS6=\"YES\",BQ6=\"\",BV6=\"YES\"),3,\"\")", "=IF(AND(BJ7=\"YES\",BK7=\"YES\",BN7=\"YES\",BS7=\"YES\",BQ7=\"\",BV7=\"YES\"),3,\"\")", "=IF(AND(BJ8=\"YES\",BK8=\"YES\",BN8=\"YES\",BS8=\"YES\",BQ8=\"\",BV8=\"YES\"),3,\"\")"], "SELL 4 (PREM DROP)": ["=IF(AND(BJ4=\"YES\",BK4=\"YES\",BS4=\"YES\",BQ4=\"YES\"),4,\"\")", "=IF(AND(BJ5=\"YES\",BK5=\"YES\",BS5=\"YES\",BQ5=\"YES\"),4,\"\")", "=IF(AND(BJ6=\"YES\",BK6=\"YES\",BS6=\"YES\",BQ6=\"YES\"),4,\"\")", "=IF(AND(BJ7=\"YES\",BK7=\"YES\",BS7=\"YES\",BQ7=\"YES\"),4,\"\")", "=IF(AND(BJ8=\"YES\",BK8=\"YES\",BS8=\"YES\",BQ8=\"YES\"),4,\"\")"], "Sell 5 (HINT PRICE SPIKE)": ["=IF(AND(BJ4=\"YES\",BK4=\"YES\",BS4=\"YES\",OR(BH4=\"YES\")),\"5\",\"\")", "=IF(AND(BJ5=\"YES\",BK5=\"YES\",BS5=\"YES\",OR(BH5=\"YES\")),\"5\",\"\")", "=IF(AND(BJ6=\"YES\",BK6=\"YES\",BS6=\"YES\",OR(BH6=\"YES\")),\"5\",\"\")", "=IF(AND(BJ7=\"YES\",BK7=\"YES\",BS7=\"YES\",OR(BH7=\"YES\")),\"5\",\"\")", "=IF(AND(BJ8=\"YES\",BK8=\"YES\",BS8=\"YES\",OR(BH8=\"YES\")),\"5\",\"\")"], "SELL with IV": ["=IF(AND(OR(CH3=\"SELL1\",CI3=\"SELL2\",CJ3=\"SELL3\",CK3=\"SELL4\"),#REF!<>\"\"),\"SELL with IV\",\"\")", "=IF(AND(OR(CH4=\"SELL1\",CI4=\"SELL2\",CJ4=\"SELL3\",CK4=\"SELL4\",CL4=\"SELL5\"),#REF!<>\"\"),\"SELL with IV\",\"\")", "=IF(AND(OR(CH5=\"SELL1\",CI5=\"SELL2\",CJ5=\"SELL3\",CK5=\"SELL4\",CL5=\"SELL5\"),#REF!<>\"\"),6,\"\")", "=IF(AND(OR(CH6=\"SELL1\",CI6=\"SELL2\",CJ6=\"SELL3\",CK6=\"SELL4\",CL6=\"SELL5\"),#REF!<>\"\"),6,\"\")", "=IF(AND(OR(CH7=\"SELL1\",CI7=\"SELL2\",CJ7=\"SELL3\",CK7=\"SELL4\",CL7=\"SELL5\"),#REF!<>\"\"),6,\"\")"], "GAMMA PRESENT": ["=IF(AND(OR(CH3=\"SELL1\",CI3=\"SELL2\",CJ3=\"SELL3\",CK3=\"SELL4\"),BX3<>\"\"),\"AVOID SELL with GAMMA\",\"\")", "=IF(AND(OR(CH4=\"SELL1\",CI4=\"SELL2\",CJ4=\"SELL3\",CK4=\"SELL4\"),BX4<>\"\"),\"AVOID SELL with GAMMA\",\"\")", "=IF(AND(OR(CH5=\"SELL1\",CI5=\"SELL2\",CJ5=\"SELL3\",CK5=\"SELL4\"),BX5<>\"\"),\"AVOID SELL with GAMMA\",\"\")", "=IF(AND(OR(CH6=\"SELL1\",CI6=\"SELL2\",CJ6=\"SELL3\",CK6=\"SELL4\"),BX6<>\"\"),\"AVOID SELL with GAMMA\",\"\")", "=IF(AND(OR(CH7=\"SELL1\",CI7=\"SELL2\",CJ7=\"SELL3\",CK7=\"SELL4\"),BX7<>\"\"),\"AVOID SELL with GAMMA\",\"\")"], "SELL COUNT": ["=IF(SUM(CH5:CM5)>0,SUM(CH5:CM5)+CO4,0)", "=IF(SUM(CH6:CM6)>0,SUM(CH6:CM6)+CO5,0)", "=IF(SUM(CH7:CM7)>0,SUM(CH7:CM7)+CO6,0)", "=IF(SUM(CH8:CM8)>0,SUM(CH8:CM8)+CO7,0)", "=IF(SUM(CH9:CM9)>0,SUM(CH9:CM9)+CO8,0)"], "SELL Count": ["=IF(COUNTA(CH4:CM4)-COUNTBLANK(CH4:CM4)>=1,(COUNTA(CH4:CM4)-COUNTBLANK(CH4:CM4)+CP3),0)", "=IF(COUNTA(CH5:CM5)-COUNTBLANK(CH5:CM5)>=1,(COUNTA(CH5:CM5)-COUNTBLANK(CH5:CM5)+CP4),0)", "=IF(COUNTA(CH6:CM6)-COUNTBLANK(CH6:CM6)>=1,(COUNTA(CH6:CM6)-COUNTBLANK(CH6:CM6)+CP5),0)", "=IF(COUNTA(CH7:CM7)-COUNTBLANK(CH7:CM7)>=1,(COUNTA(CH7:CM7)-COUNTBLANK(CH7:CM7)+CP6),0)", "=IF(COUNTA(CH8:CM8)-COUNTBLANK(CH8:CM8)>=1,(COUNTA(CH8:CM8)-COUNTBLANK(CH8:CM8)+CP7),0)"], "PRICE": ["=J2", "=J3", "=J4", "=J5", "=J6"], "TIMES": ["=B2", "=B3", "=B4", "=B5", "=B6"]}