# ClickHouse ↔ Polars Type Mapping Reference

## Overview

This document provides a comprehensive reference for mapping between Polars and ClickHouse data types in the IndicatorsUsingPolars system.

## Type Mapping Configuration

In `config/output_columns.yaml`, each column must specify both the Polars type and the ClickHouse type:

```yaml
columns:
  column_name:
    polars_type: "Float32"           # Polars type for DataFrame
    clickhouse_type: "Float32"       # ClickHouse type for table
    nullable: true                   # Whether column can be NULL
    category: "technical_indicators"
    description: "Column description"
    default: 0.0
```

## Supported Type Mappings

### Numeric Types

| Polars Type | ClickHouse Type | Nullable Version | Description | Example |
|-------------|-----------------|------------------|-------------|---------|
| `Float32` | `Float32` | `Nullable(Float32)` | 32-bit floating point | `123.45` |
| `Float64` | `Float64` | `Nullable(Float64)` | 64-bit floating point | `123.456789` |
| `Int8` | `Int8` | `Nullable(Int8)` | 8-bit signed integer | `-128 to 127` |
| `Int16` | `Int16` | `Nullable(Int16)` | 16-bit signed integer | `-32768 to 32767` |
| `Int32` | `Int32` | `Nullable(Int32)` | 32-bit signed integer | `-2B to 2B` |
| `Int64` | `Int64` | `Nullable(Int64)` | 64-bit signed integer | Large integers |
| `UInt8` | `UInt8` | `Nullable(UInt8)` | 8-bit unsigned integer | `0 to 255` |
| `UInt16` | `UInt16` | `Nullable(UInt16)` | 16-bit unsigned integer | `0 to 65535` |
| `UInt32` | `UInt32` | `Nullable(UInt32)` | 32-bit unsigned integer | `0 to 4B` |
| `UInt64` | `UInt64` | `Nullable(UInt64)` | 64-bit unsigned integer | Large positive integers |

### String Types

| Polars Type | ClickHouse Type | Nullable Version | Description | Example |
|-------------|-----------------|------------------|-------------|---------|
| `Utf8` | `String` | `Nullable(String)` | Variable-length string | `"NIFTY"` |
| `Utf8` | `FixedString(N)` | `Nullable(FixedString(N))` | Fixed-length string | `"ABCD"` (N=4) |

### Date/Time Types

| Polars Type | ClickHouse Type | Nullable Version | Description | Example |
|-------------|-----------------|------------------|-------------|---------|
| `Date` | `Date` | `Nullable(Date)` | Date only | `2025-10-05` |
| `Datetime` | `DateTime` | `Nullable(DateTime)` | Date and time | `2025-10-05 16:30:00` |
| `Datetime` | `DateTime64(3)` | `Nullable(DateTime64(3))` | High precision timestamp | Millisecond precision |

### Boolean Types

| Polars Type | ClickHouse Type | Nullable Version | Description | Example |
|-------------|-----------------|------------------|-------------|---------|
| `Boolean` | `Bool` | `Nullable(Bool)` | True/false values | `true`, `false` |
| `Boolean` | `UInt8` | `Nullable(UInt8)` | Boolean as integer | `0`, `1` |

### Enum Types

| Polars Type | ClickHouse Type | Description | Example |
|-------------|-----------------|-------------|---------|
| `Utf8` | `Enum8('val1'=1, 'val2'=2)` | 8-bit enum | Symbol types |
| `Utf8` | `Enum16('val1'=1, 'val2'=2)` | 16-bit enum | Larger enums |

### Special ClickHouse Types

| ClickHouse Type | Description | Use Case |
|-----------------|-------------|----------|
| `LowCardinality(String)` | Optimized for repeated strings | Categories with few unique values |
| `Decimal(P,S)` | Fixed-point decimal | Financial calculations |
| `Array(Type)` | Array of elements | Lists of values |
| `Tuple(Type1, Type2)` | Fixed structure | Composite values |

## Configuration Examples

### Basic Numeric Column
```yaml
rsi_last_price:
  polars_type: "Float32"
  clickhouse_type: "Float32"
  nullable: true
  category: "technical_indicators"
  description: "RSI calculated on last_price"
  default: 0.0
```

### Enum Column (Symbol)
```yaml
symbol:
  polars_type: "Utf8"
  clickhouse_type: "Enum8('UNKNOWN'=0, 'NIFTY'=1, 'BANKNIFTY'=2, 'FINNIFTY'=3, 'MIDCPNIFTY'=4)"
  nullable: false
  category: "key_columns"
  description: "Trading symbol"
  required: true
```

### Enum Column (Expiry Type)
```yaml
expiry_type:
  polars_type: "Utf8"
  clickhouse_type: "Enum8('UNKNOWN'=0, 'CE'=1, 'PE'=2, 'FUTURE'=3)"
  nullable: false
  category: "key_columns"
  description: "Option type"
  required: true
```

### Timestamp with Default
```yaml
calculation_timestamp:
  polars_type: "Datetime"
  clickhouse_type: "DateTime DEFAULT now()"
  nullable: false
  category: "metadata"
  description: "When indicators were calculated"
  required: true
```

### String Column
```yaml
buy_primary:
  polars_type: "Utf8"
  clickhouse_type: "String"
  nullable: true
  category: "trading_signals"
  description: "Primary buy signal description"
  default: ""
```

### Count Column
```yaml
standard_buy_count:
  polars_type: "UInt32"
  clickhouse_type: "UInt32"
  nullable: true
  category: "trading_signals"
  description: "Count of standard buy signals"
  default: 0
```

### High Precision Decimal
```yaml
precise_price:
  polars_type: "Float64"
  clickhouse_type: "Decimal(18,8)"
  nullable: true
  category: "custom_indicators"
  description: "High precision price calculation"
  default: 0.0
```

### Low Cardinality String
```yaml
signal_category:
  polars_type: "Utf8"
  clickhouse_type: "LowCardinality(String)"
  nullable: true
  category: "trading_signals"
  description: "Signal category (few unique values)"
  default: ""
```

## Best Practices

### 1. Nullable vs Non-Nullable
- **Key columns**: Usually non-nullable (`nullable: false`)
- **Data columns**: Usually nullable (`nullable: true`)
- **Metadata**: Usually non-nullable with defaults

### 2. Enum Usage
- Use `Enum8` for up to 127 values
- Use `Enum16` for up to 32767 values
- Always include 'UNKNOWN' = 0 as default

### 3. Performance Considerations
- Use `LowCardinality(String)` for columns with few unique values
- Use `FixedString(N)` for fixed-length strings
- Use appropriate integer sizes (UInt8 vs UInt32)

### 4. Default Values
- Numeric: `0` or `0.0`
- String: `""` (empty string)
- Boolean: `false`
- Enum: First value (usually 'UNKNOWN')

## Validation Rules

1. **polars_type** must be a valid Polars type
2. **clickhouse_type** must be valid ClickHouse SQL
3. **nullable** determines if `Nullable()` wrapper is added
4. **required** columns cannot have NULL values
5. **default** must match the data type

## Common Patterns

### Financial Data
```yaml
price: {polars_type: "Float32", clickhouse_type: "Float32", nullable: true}
volume: {polars_type: "UInt64", clickhouse_type: "UInt64", nullable: true}
```

### Signals
```yaml
signal_flag: {polars_type: "Boolean", clickhouse_type: "Bool", nullable: true}
signal_strength: {polars_type: "Float32", clickhouse_type: "Float32", nullable: true}
```

### Identifiers
```yaml
id: {polars_type: "UInt64", clickhouse_type: "UInt64", nullable: false}
name: {polars_type: "Utf8", clickhouse_type: "String", nullable: false}
```
