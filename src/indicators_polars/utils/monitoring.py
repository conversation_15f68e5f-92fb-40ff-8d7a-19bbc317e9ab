"""
Monitoring and performance tracking utilities
"""

import time
import logging
from typing import Dict, Any, Optional
from contextlib import contextmanager
from dataclasses import dataclass, field
from datetime import datetime


logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """Performance metrics tracking"""
    start_time: float = field(default_factory=time.time)
    end_time: Optional[float] = None
    duration: Optional[float] = None
    rows_processed: int = 0
    errors_count: int = 0
    memory_usage_mb: Optional[float] = None
    
    def finish(self):
        """Mark the operation as finished"""
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
    
    @property
    def rows_per_second(self) -> float:
        """Calculate rows processed per second"""
        if self.duration and self.duration > 0:
            return self.rows_processed / self.duration
        return 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert metrics to dictionary"""
        return {
            'start_time': datetime.fromtimestamp(self.start_time).isoformat(),
            'end_time': datetime.fromtimestamp(self.end_time).isoformat() if self.end_time else None,
            'duration_seconds': self.duration,
            'rows_processed': self.rows_processed,
            'rows_per_second': self.rows_per_second,
            'errors_count': self.errors_count,
            'memory_usage_mb': self.memory_usage_mb
        }


class PerformanceMonitor:
    """Monitor performance and latency of operations"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config.get('monitoring', {})
        self.enabled = self.config.get('enabled', True)
        self.latency_threshold = self.config.get('latency_threshold_seconds', 45)
        self.error_threshold = self.config.get('error_threshold_count', 5)
        self.metrics_history: list[PerformanceMetrics] = []
    
    @contextmanager
    def track_operation(self, operation_name: str, expected_rows: int = 0):
        """Context manager to track operation performance"""
        if not self.enabled:
            yield None
            return
        
        metrics = PerformanceMetrics()
        metrics.rows_processed = expected_rows
        
        logger.debug(f"Starting operation: {operation_name}")
        
        try:
            yield metrics
            metrics.finish()
            
            # Log performance metrics
            self._log_metrics(operation_name, metrics)
            
            # Check thresholds
            self._check_thresholds(operation_name, metrics)
            
            # Store metrics
            self.metrics_history.append(metrics)
            
            # Keep only last 100 metrics
            if len(self.metrics_history) > 100:
                self.metrics_history = self.metrics_history[-100:]
                
        except Exception as e:
            metrics.errors_count += 1
            metrics.finish()
            logger.error(f"Operation {operation_name} failed: {e}")
            self.metrics_history.append(metrics)
            raise
    
    def _log_metrics(self, operation_name: str, metrics: PerformanceMetrics):
        """Log performance metrics"""
        logger.debug(
            f"Operation {operation_name} completed - "
            f"Duration: {metrics.duration:.2f}s, "
            f"Rows: {metrics.rows_processed}, "
            f"Rate: {metrics.rows_per_second:.0f} rows/s"
        )
    
    def _check_thresholds(self, operation_name: str, metrics: PerformanceMetrics):
        """Check if metrics exceed thresholds"""
        if metrics.duration and metrics.duration > self.latency_threshold:
            logger.warning(
                f"Operation {operation_name} exceeded latency threshold: "
                f"{metrics.duration:.2f}s > {self.latency_threshold}s"
            )
        
        if metrics.errors_count > self.error_threshold:
            logger.error(
                f"Operation {operation_name} exceeded error threshold: "
                f"{metrics.errors_count} > {self.error_threshold}"
            )
    
    def get_recent_metrics(self, count: int = 10) -> list[Dict[str, Any]]:
        """Get recent performance metrics"""
        recent = self.metrics_history[-count:] if self.metrics_history else []
        return [metrics.to_dict() for metrics in recent]
    
    def get_average_performance(self) -> Dict[str, float]:
        """Get average performance metrics"""
        if not self.metrics_history:
            return {}
        
        total_duration = sum(m.duration for m in self.metrics_history if m.duration)
        total_rows = sum(m.rows_processed for m in self.metrics_history)
        total_errors = sum(m.errors_count for m in self.metrics_history)
        
        count = len(self.metrics_history)
        
        return {
            'average_duration': total_duration / count if count > 0 else 0,
            'average_rows_per_second': total_rows / total_duration if total_duration > 0 else 0,
            'total_errors': total_errors,
            'error_rate': total_errors / count if count > 0 else 0
        }
