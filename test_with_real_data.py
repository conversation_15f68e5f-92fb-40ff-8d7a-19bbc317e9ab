#!/usr/bin/env python3
"""
Test the indicators system with real data from sensibull_index_02_09_2025
"""

import sys
from pathlib import Path
from datetime import datetime, date
import polars as pl

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from indicators_polars.utils.config_loader import ConfigLoader
from indicators_polars.core.config_manager import ConfigManager
from indicators_polars.database.clickhouse_client import ClickHouseClient
from indicators_polars.core.indicator_engine import IndicatorEngine
from indicators_polars.utils.monitoring import PerformanceMonitor
from indicators_polars.utils.logging_config import setup_logging


def test_with_real_data():
    """Test the system with real data from ClickHouse"""
    print("Testing IndicatorsUsingPolars with real data")
    print("=" * 50)
    
    try:
        # Load configuration
        config_loader = ConfigLoader('config.yaml')
        config_dict = config_loader.load()
        
        # Setup logging
        logger = setup_logging(config_dict)
        logger.info("Starting real data test")
        
        # Create components
        config_manager = ConfigManager(config_dict)
        performance_monitor = PerformanceMonitor(config_dict)
        clickhouse_client = ClickHouseClient(config_dict)
        indicator_engine = IndicatorEngine(config_manager, performance_monitor)
        
        # Test connection
        print("Testing ClickHouse connection...")
        if not clickhouse_client.test_connection():
            print("✗ ClickHouse connection failed")
            return False
        print("✓ ClickHouse connection successful")
        
        # Check if the test table exists
        test_table = "sensibull_index_02_09_2025"
        print(f"Checking if table {test_table} exists...")
        
        if not clickhouse_client.table_exists(test_table):
            print(f"✗ Table {test_table} does not exist")
            return False
        print(f"✓ Table {test_table} exists")
        
        # Fetch sample data (limit to 1000 rows for testing)
        print("Fetching sample data...")
        target_date = date(2025, 9, 2)
        
        # Get data for a specific symbol and time range
        sample_data = clickhouse_client.get_data_for_date(
            target_date=target_date,
            symbols=["NIFTY"],  # Test with just NIFTY
            start_time="09:15",
            end_time="10:00"    # Just first 45 minutes for testing
        )
        
        if sample_data.is_empty():
            print("✗ No data found for the test period")
            return False
        
        print(f"✓ Fetched {len(sample_data)} rows of sample data")
        print(f"  Columns: {sample_data.columns}")
        print(f"  Date range: {sample_data.select(pl.col('timestamp').min().alias('min_time'), pl.col('timestamp').max().alias('max_time'))}")
        
        # Test indicator calculation
        print("\nTesting indicator calculation...")
        
        with performance_monitor.track_operation("test_calculation", len(sample_data)) as metrics:
            result_df = indicator_engine.calculate_indicators(sample_data, delta_only=False)
        
        if result_df.is_empty():
            print("✗ No indicators calculated")
            return False
        
        print(f"✓ Calculated indicators for {len(result_df)} rows")
        print(f"  Processing time: {metrics.duration:.2f} seconds")
        print(f"  Rows per second: {metrics.rows_per_second:.0f}")
        
        # Show sample of calculated indicators
        print("\nSample of calculated indicators:")
        indicator_columns = indicator_engine.get_all_indicator_columns()
        print(f"Total indicator columns: {len(indicator_columns)}")
        
        # Show first few rows of key indicators
        key_indicators = [col for col in indicator_columns if col in result_df.columns][:10]
        if key_indicators:
            sample_result = result_df.select(
                ["symbol", "timestamp", "strike", "expiry_type"] + key_indicators
            ).head(5)
            print(sample_result)
        
        # Test performance with larger dataset
        print(f"\nTesting with larger dataset...")
        larger_data = clickhouse_client.get_data_for_date(
            target_date=target_date,
            symbols=["NIFTY", "BANKNIFTY"],  # Two symbols
            start_time="09:15",
            end_time="11:00"    # 1 hour 45 minutes
        )
        
        if not larger_data.is_empty():
            print(f"Larger dataset: {len(larger_data)} rows")
            
            with performance_monitor.track_operation("large_test", len(larger_data)) as metrics:
                large_result = indicator_engine.calculate_indicators(larger_data, delta_only=False)
            
            print(f"✓ Processed {len(large_result)} rows in {metrics.duration:.2f} seconds")
            print(f"  Rows per second: {metrics.rows_per_second:.0f}")
            
            # Check if we meet the performance requirement (1.2M rows in < 1 minute)
            if metrics.rows_per_second > 20000:  # 1.2M / 60 seconds
                print("✓ Performance requirement likely met")
            else:
                print("⚠ Performance may not meet requirement for 1.2M rows")
        
        # Test output table creation
        print(f"\nTesting output table creation...")
        output_table = config_manager.output.table_name
        
        if clickhouse_client.create_output_table(output_table):
            print(f"✓ Output table {output_table} created/verified")
            
            # Test data insertion
            print("Testing data insertion...")
            success = clickhouse_client.insert_indicators_data(
                result_df.head(100),  # Insert just 100 rows for testing
                output_table,
                batch_size=50
            )
            
            if success:
                print("✓ Data insertion successful")
            else:
                print("✗ Data insertion failed")
        else:
            print(f"✗ Failed to create output table {output_table}")
        
        print("\n" + "=" * 50)
        print("Real data test completed successfully!")
        return True
        
    except Exception as e:
        print(f"✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    success = test_with_real_data()
    sys.exit(0 if success else 1)
