# IndicatorsUsingPolars

High-performance options trading indicators calculation engine using Polars and TA-Lib for real-time and historical analysis.

## Features

- **High Performance**: Built with Polars for fast data processing
- **Real-time Processing**: Live mode with delta updates every minute
- **Historical Analysis**: Progressive time window calculations
- **Flexible Indicators**: Configurable technical and custom indicators
- **Parallel Processing**: Multi-threaded partition-based calculations
- **ClickHouse Integration**: Optimized for time series data storage
- **Comprehensive Monitoring**: Performance tracking and latency monitoring
- **Robust Logging**: Console and daily rotating file logs

## Architecture

### Core Components

1. **Indicator Engine**: Main calculation engine with partitioning support
2. **Technical Indicators**: RSI, MACD, SMA, EMA using polars_talib
3. **Custom Indicators**: Excel-based formulas for options trading signals
4. **Live Processor**: Real-time delta processing with market hours awareness
5. **Historical Processor**: Progressive window analysis for backtesting
6. **ClickHouse Client**: Optimized database operations with connection pooling

### Data Partitioning

Data is partitioned by:
- Symbol (NIFTY, BANKNIFTY, MIDCPNIFTY, FINNIFTY)
- Expiry Date
- Expiry Type (CE, PE, FUTURE)
- Strike Price

## Installation

### Prerequisites

- Python 3.8+
- ClickHouse database
- Virtual environment (recommended)

### Setup

1. **Clone and setup environment**:
```bash
git clone <repository>
cd IndicatorsUsingPolars
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. **Install dependencies**:
```bash
pip install -r requirements.txt
```

3. **Configure database**:
   - Update `config.yaml` with your ClickHouse credentials
   - Ensure tables exist with the structure defined in `tableStructure.txt`

## Configuration

Edit `config.yaml` to customize:

### Database Settings
```yaml
database:
  clickhouse:
    host: "localhost"
    port: 8123
    username: "your_username"
    password: "your_password"
    database: "your_database"
```

### Market Configuration
```yaml
market:
  symbols: ["NIFTY", "BANKNIFTY", "MIDCPNIFTY", "FINNIFTY"]
  start_time: "09:15"
  end_time: "15:30"
  timezone: "Asia/Kolkata"
```

### Indicators
```yaml
indicators:
  technical:
    rsi:
      enabled: true
      period: 14
    macd:
      enabled: true
      fast_period: 12
      slow_period: 26
  custom:
    rankings:
      enabled: true
      window_size: 100
```

## Usage

### Live Mode
Process real-time data with delta updates:
```bash
python src/indicators_polars/main.py --mode live
```

### Historical Analysis
Analyze historical data for a specific date:
```bash
python src/indicators_polars/main.py --mode historical --date 2025-01-13
```

### Historical Range
Process multiple days:
```bash
python src/indicators_polars/main.py --mode historical --start-date 2025-01-13 --end-date 2025-01-15
```

### Progressive Analysis
Simulate intraday progression:
```bash
python src/indicators_polars/main.py --mode historical --date 2025-01-13 --progressive
```

### Validation Only
Test configuration without processing:
```bash
python src/indicators_polars/main.py --mode live --validate-only
```

## Command Line Options

```
--config, -c          Configuration file path (default: config.yaml)
--mode, -m           Processing mode: live or historical
--date, -d           Date for historical analysis (YYYY-MM-DD)
--start-date         Start date for range analysis
--end-date           End date for range analysis
--log-level          Override log level (DEBUG, INFO, WARNING, ERROR)
--validate-only      Only validate setup, don't run processing
--progressive        Use progressive intraday analysis
```

## Indicators

### Technical Indicators (via polars_talib)
- **RSI**: Relative Strength Index
- **MACD**: Moving Average Convergence Divergence
- **SMA**: Simple Moving Average (multiple periods)
- **EMA**: Exponential Moving Average (multiple periods)

### Custom Indicators (Excel-based)
- **Rankings**: PERCENTRANK equivalent for OI, Greeks, IV
- **Intrinsic Value**: CE/PE intrinsic value calculations
- **Pattern Detection**: Consecutive drops, builds, candle patterns
- **Trading Signals**: 5 BUY and 5 SELL signal types
- **Special Conditions**: Gamma moves, IV spikes, Vega cooling

## Performance

### Optimization Features
- **Parallel Processing**: Multi-threaded partition processing
- **Delta Mode**: Only process new data in live mode
- **Connection Pooling**: Efficient database connections
- **Batch Operations**: Optimized insert/update operations
- **Memory Management**: Efficient Polars DataFrame operations

### Monitoring
- **Latency Tracking**: Processing time monitoring
- **Error Counting**: Failure rate tracking
- **Performance Metrics**: Rows/second, memory usage
- **Threshold Alerts**: Configurable performance alerts

## Testing

Run basic functionality tests:
```bash
python tests/test_basic_functionality.py
```

## Logging

Logs are written to:
- **Console**: Real-time output with configurable level
- **File**: Daily rotating logs in `logs/indicators.log`
- **Retention**: 30 days of log history

## Error Handling

- **Database Reconnection**: Automatic reconnection on failures
- **Graceful Degradation**: Continue processing on non-critical errors
- **Comprehensive Logging**: Detailed error tracking and context
- **Monitoring Integration**: Error rate tracking and alerting

## Market Hours

- **Live Mode**: Only processes during market hours (09:15-15:30 IST)
- **Weekend Handling**: Automatically skips weekends in historical mode
- **Timezone Aware**: Proper timezone handling for market hours

## Output

Results are stored in ClickHouse with schema including:
- Original market data (key columns)
- Technical indicators (RSI, MACD, SMA, EMA)
- Custom indicators (rankings, patterns, signals)
- Metadata (calculation timestamp)

## Troubleshooting

### Common Issues

1. **Database Connection**: Check ClickHouse credentials and network connectivity
2. **Missing Tables**: Ensure source tables exist for the target date
3. **Performance**: Adjust worker count and batch sizes for your hardware
4. **Memory**: Monitor memory usage with large datasets

### Debug Mode
```bash
python src/indicators_polars/main.py --mode live --log-level DEBUG
```

## Contributing

1. Follow the existing code structure
2. Add tests for new functionality
3. Update configuration schema as needed
4. Maintain performance benchmarks

## License

[Add your license information here]
