-- sensibull.sensibull_index_05_06_2025 definition

CREATE TABLE sensibull.sensibull_index_05_06_2025
(

    `symbol` Enum8('UNKNOWN' = 0,
 'NIFTY' = 1,
 'BANKNIFTY' = 2,
 'FINNIFTY' = 3,
 'MIDCPNIFTY' = 4),

    `timestamp` DateTime,

    `expiry_date` Date,

    `current_price` Nullable(Float32),

    `strike` Float32,

    `strike_type` Enum8('UNKNOWN' = 0,
 'NEAR' = 1,
 'FAR' = 2,
 'FUTURE' = 3),

    `lot_size` Nullable(UInt16),

    `expiry_type` Enum8('UNKNOWN' = 0,
 'CE' = 1,
 'PE' = 2,
 'FUTURE' = 3),

    `moneyness` Enum8('UNKNOWN' = 0,
 'ITM' = 1,
 'OTM' = 2,
 'DOTM' = 3,
 'DITM' = 4,
 'ATM' = 5),

    `last_price` Nullable(Float32),

    `ltp_change` Nullable(Float32),

    `oi` Nullable(Float32),

    `oi_change` Nullable(Float32),

    `volume` Nullable(Float32),

    `is_liquid` Nullable(UInt8),

    `theta` Nullable(Float32),

    `delta` Nullable(Float32),

    `gamma` Nullable(Float32),

    `vega` Nullable(Float32),

    `iv` Nullable(Float32),

    `batch` Nullable(UInt32),

    `prev_current_price` Nullable(Float32),

    `prev_last_price` Nullable(Float32),

    `prev_oi` Nullable(Float32),

    `prev_volume` Nullable(Float32),

    `prev_delta` Nullable(Float32),

    `prev_gamma` Nullable(Float32),

    `prev_iv` Nullable(Float32),

    `delta_current_price` Nullable(Float32),

    `delta_last_price` Nullable(Float32),

    `delta_oi` Nullable(Float32),

    `delta_volume` Nullable(Float32),

    `delta_delta` Nullable(Float32),

    `delta_gamma` Nullable(Float32),

    `delta_iv` Nullable(Float32),

    INDEX idx_symbol symbol TYPE minmax GRANULARITY 1,

    INDEX idx_expiry_date expiry_date TYPE minmax GRANULARITY 1,

    INDEX idx_strike (symbol,
 strike,
 expiry_date,
 expiry_type) TYPE minmax GRANULARITY 1,

    INDEX idx_timestamp_symbol (timestamp,
 symbol,
 expiry_type) TYPE minmax GRANULARITY 1,

    INDEX idx_strike_oi_vol (moneyness,
 strike_type,
 delta_oi) TYPE minmax GRANULARITY 1
)
ENGINE = MergeTree
ORDER BY (timestamp,
 symbol,
 expiry_date,
 expiry_type,
 strike,
 strike_type)
SETTINGS index_granularity = 8192;