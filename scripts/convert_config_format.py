#!/usr/bin/env python3
"""
Script to convert output_columns.yaml from old format to new format
with explicit polars_type and clickhouse_type mappings
"""

import yaml
import sys
from pathlib import Path

def convert_type_to_new_format(old_config):
    """Convert old format to new format with explicit type mappings"""
    
    # Type mapping rules
    def get_clickhouse_type(polars_type, column_name, category):
        """Get appropriate ClickHouse type based on Polars type and context"""
        
        # Special enum columns
        if column_name == 'symbol':
            return "Enum8('UNKNOWN'=0, 'NIFTY'=1, 'BANKNIFTY'=2, 'FINNIFTY'=3, 'MIDCPNIFTY'=4)"
        elif column_name == 'expiry_type':
            return "Enum8('UNKNOWN'=0, 'CE'=1, 'PE'=2, 'FUTURE'=3)"
        elif column_name == 'calculation_timestamp':
            return "DateTime DEFAULT now()"
        
        # String columns that should be LowCardinality for performance
        string_low_cardinality = [
            'iv_vega_condition', 'gamma_condition', 'buy_primary', 
            'buy_speicial', 'buy_special_desc', 'short_covering_move', 
            'short_covering_desc'
        ]
        
        # Direct type mappings
        type_mapping = {
            'Utf8': 'String',
            'Datetime': 'DateTime', 
            'Date': 'Date',
            'Boolean': 'Bool',
            'Float32': 'Float32',
            'Float64': 'Float64',
            'Int8': 'Int8',
            'Int16': 'Int16', 
            'Int32': 'Int32',
            'Int64': 'Int64',
            'UInt8': 'UInt8',
            'UInt16': 'UInt16',
            'UInt32': 'UInt32',
            'UInt64': 'UInt64'
        }
        
        base_type = type_mapping.get(polars_type, polars_type)
        
        # Use LowCardinality for specific string columns
        if polars_type == 'Utf8' and column_name in string_low_cardinality:
            base_type = 'LowCardinality(String)'
        
        return base_type
    
    def is_nullable(column_name, category, required):
        """Determine if column should be nullable"""
        # Key columns and required columns are not nullable
        if category == 'key_columns' or required:
            return False
        # Metadata columns are usually not nullable
        if category == 'metadata':
            return False
        # Everything else is nullable
        return True
    
    # Convert columns
    new_columns = {}
    
    for column_name, column_config in old_config['columns'].items():
        # Get old values
        old_type = column_config.get('type', 'Utf8')
        category = column_config.get('category', 'custom_indicators')
        description = column_config.get('description', '')
        default = column_config.get('default')
        required = column_config.get('required', False)
        
        # Determine nullable status
        nullable = is_nullable(column_name, category, required)
        
        # Get ClickHouse type
        clickhouse_type = get_clickhouse_type(old_type, column_name, category)
        
        # Add Nullable wrapper if needed
        if nullable and not clickhouse_type.startswith(('Enum', 'DateTime DEFAULT')):
            clickhouse_type = f"Nullable({clickhouse_type})"
        
        # Create new column config
        new_column_config = {
            'polars_type': old_type,
            'clickhouse_type': clickhouse_type,
            'nullable': nullable,
            'category': category,
            'description': description
        }
        
        if required:
            new_column_config['required'] = True
            
        if default is not None:
            new_column_config['default'] = default
            
        new_columns[column_name] = new_column_config
    
    # Create new config structure
    new_config = {
        'column_categories': old_config['column_categories'],
        'columns': new_columns
    }
    
    return new_config

def main():
    config_path = Path('config/output_columns.yaml')
    
    if not config_path.exists():
        print(f"Error: {config_path} not found")
        sys.exit(1)
    
    # Load current config
    with open(config_path, 'r') as f:
        current_config = yaml.safe_load(f)
    
    # Check if already converted
    first_column = next(iter(current_config['columns'].values()))
    if 'polars_type' in first_column:
        print("Config already appears to be in new format")
        return
    
    print("Converting config to new format...")
    
    # Convert to new format
    new_config = convert_type_to_new_format(current_config)
    
    # Create backup
    backup_path = config_path.with_suffix('.yaml.backup')
    with open(backup_path, 'w') as f:
        yaml.dump(current_config, f, default_flow_style=False, sort_keys=False)
    print(f"Backup created: {backup_path}")
    
    # Write new config with header
    header = '''# Output Columns Configuration
# This file defines all output columns for the indicators system
# It serves as the single source of truth for schema definition

# VALID TYPES:
# - Utf8: String/text data
# - Float32: 32-bit floating point numbers
# - Float64: 64-bit floating point numbers  
# - UInt8, UInt16, UInt32, UInt64: Unsigned integers
# - Int8, Int16, Int32, Int64: Signed integers
# - Datetime: Date and time
# - Date: Date only
# - Boolean: True/false values

# VALID CATEGORIES:
# - key_columns: Primary identification columns (required)
# - original_data: Source data from input table
# - technical_indicators: RSI, MACD, SMA, EMA, etc.
# - ranking_indicators: Percentile rankings
# - custom_indicators: Business logic indicators
# - pattern_detection: Pattern recognition columns
# - trading_signals: Buy/sell signals
# - metadata: System metadata

# Column Categories and Metadata
'''
    
    with open(config_path, 'w') as f:
        f.write(header)
        yaml.dump(new_config, f, default_flow_style=False, sort_keys=False)
    
    print(f"Config converted successfully!")
    print(f"Columns converted: {len(new_config['columns'])}")

if __name__ == '__main__':
    main()
