"""
Main application entry point for IndicatorsUsingPolars
"""

import sys
import argparse
import logging
from datetime import datetime, date
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from indicators_polars.utils.config_loader import ConfigLoader
from indicators_polars.utils.logging_config import setup_logging
from indicators_polars.utils.monitoring import PerformanceMonitor
from indicators_polars.core.config_manager import ConfigManager
from indicators_polars.core.indicator_engine import IndicatorEngine
from indicators_polars.core.historical_processor import HistoricalProcessor
from indicators_polars.core.live_processor import LiveProcessor
from indicators_polars.database.clickhouse_client import ClickHouseClient


def create_argument_parser():
    """Create command line argument parser"""
    parser = argparse.ArgumentParser(
        description="IndicatorsUsingPolars - High-performance options trading indicators",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run in live mode
  python main.py --mode live
  
  # Run historical analysis for specific date
  python main.py --mode historical --date 2025-01-13
  
  # Run historical analysis for date range
  python main.py --mode historical --start-date 2025-01-13 --end-date 2025-01-15
  
  # Use custom config file
  python main.py --config custom_config.yaml --mode live
  
  # Run with debug logging
  python main.py --mode live --log-level DEBUG
        """
    )
    
    parser.add_argument(
        '--config', '-c',
        type=str,
        default='config.yaml',
        help='Path to configuration file (default: config.yaml)'
    )
    
    parser.add_argument(
        '--mode', '-m',
        choices=['live', 'historical'],
        required=True,
        help='Processing mode: live or historical'
    )
    
    parser.add_argument(
        '--date', '-d',
        type=str,
        help='Date for historical analysis (YYYY-MM-DD format)'
    )
    
    parser.add_argument(
        '--start-date',
        type=str,
        help='Start date for historical range analysis (YYYY-MM-DD format)'
    )
    
    parser.add_argument(
        '--end-date',
        type=str,
        help='End date for historical range analysis (YYYY-MM-DD format)'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        help='Override log level from config'
    )
    
    parser.add_argument(
        '--validate-only',
        action='store_true',
        help='Only validate configuration and setup, do not run processing'
    )
    
    parser.add_argument(
        '--progressive',
        action='store_true',
        help='Use progressive intraday analysis for historical mode'
    )
    
    return parser


def parse_date(date_str: str) -> date:
    """Parse date string in YYYY-MM-DD format"""
    try:
        return datetime.strptime(date_str, '%Y-%m-%d').date()
    except ValueError:
        raise ValueError(f"Invalid date format: {date_str}. Use YYYY-MM-DD format.")


def main():
    """Main application entry point"""
    parser = create_argument_parser()
    args = parser.parse_args()
    
    try:
        # Load configuration
        config_loader = ConfigLoader(args.config)
        config_dict = config_loader.load()
        
        # Override log level if specified
        if args.log_level:
            config_dict['logging']['level'] = args.log_level
        
        # Setup logging
        logger = setup_logging(config_dict)
        logger.info("IndicatorsUsingPolars starting up")
        logger.info(f"Configuration loaded from: {args.config}")
        logger.info(f"Processing mode: {args.mode}")
        
        # Create configuration manager
        config_manager = ConfigManager(config_dict)
        
        # Override processing mode if specified
        if args.mode:
            config_manager.processing.mode = args.mode
        
        # Validate configuration
        if not config_manager.validate():
            logger.error("Configuration validation failed")
            return 1
        
        # Create components
        performance_monitor = PerformanceMonitor(config_dict)
        clickhouse_client = ClickHouseClient(config_dict)
        indicator_engine = IndicatorEngine(config_manager, performance_monitor)
        
        # Validation only mode
        if args.validate_only:
            logger.info("Validation completed successfully")
            return 0
        
        # Run based on mode
        if args.mode == 'live':
            return run_live_mode(
                config_manager, 
                clickhouse_client, 
                indicator_engine, 
                performance_monitor
            )
        elif args.mode == 'historical':
            return run_historical_mode(
                args,
                config_manager,
                clickhouse_client,
                indicator_engine,
                performance_monitor
            )
        else:
            logger.error(f"Unknown mode: {args.mode}")
            return 1
            
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
        return 0
    except Exception as e:
        logger.error(f"Application error: {e}", exc_info=True)
        return 1


def run_live_mode(
    config_manager: ConfigManager,
    clickhouse_client: ClickHouseClient,
    indicator_engine: IndicatorEngine,
    performance_monitor: PerformanceMonitor
) -> int:
    """Run live processing mode"""
    logger = logging.getLogger(__name__)
    logger.info("Starting live processing mode")
    
    live_processor = LiveProcessor(
        config_manager,
        clickhouse_client,
        indicator_engine,
        performance_monitor
    )
    
    success = live_processor.start_live_processing()
    return 0 if success else 1


def run_historical_mode(
    args,
    config_manager: ConfigManager,
    clickhouse_client: ClickHouseClient,
    indicator_engine: IndicatorEngine,
    performance_monitor: PerformanceMonitor
) -> int:
    """Run historical processing mode"""
    logger = logging.getLogger(__name__)
    logger.info("Starting historical processing mode")
    
    historical_processor = HistoricalProcessor(
        config_manager,
        clickhouse_client,
        indicator_engine,
        performance_monitor
    )
    
    # Determine date(s) to process
    if args.start_date and args.end_date:
        # Date range processing
        start_date = parse_date(args.start_date)
        end_date = parse_date(args.end_date)
        logger.info(f"Processing date range: {start_date} to {end_date}")
        success = historical_processor.run_historical_range(start_date, end_date)
    elif args.date:
        # Single date processing
        target_date = parse_date(args.date)
        logger.info(f"Processing single date: {target_date}")
        
        if args.progressive:
            success = historical_processor.run_progressive_intraday_analysis(target_date)
        else:
            success = historical_processor.run_historical_analysis(target_date)
    else:
        # Use date from config
        target_date = config_manager.get_processing_date()
        logger.info(f"Processing date from config: {target_date}")
        
        if args.progressive:
            success = historical_processor.run_progressive_intraday_analysis(target_date)
        else:
            success = historical_processor.run_historical_analysis(target_date)
    
    return 0 if success else 1


if __name__ == '__main__':
    sys.exit(main())
