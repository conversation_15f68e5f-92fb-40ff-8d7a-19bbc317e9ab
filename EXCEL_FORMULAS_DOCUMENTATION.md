# Excel Formulas Documentation

This document provides comprehensive documentation of all Excel formulas implemented in the indicators system, based on the `excelFormula.json` file.

## Overview

The system implements **394 Excel formulas** across multiple categories:
- **Ranking Indicators** (PERCENTRANK equivalents)
- **Pattern Detection** (consecutive drops, builds, etc.)
- **Trading Signals** (BUY/SELL conditions)
- **Special Conditions** (Gamma moves, IV spikes, etc.)

## Formula Categories

### 1. Ranking Indicators (PERCENTRANK Equivalents)

These formulas calculate percentile rankings for various metrics:

#### OI Rank
```excel
=PERCENTRANK($H$2:$H$1000,H2,3)
```
**Implementation**: `oi_rank = pl.col("oi").rank("ordinal") / pl.col("oi").count()`
**Purpose**: Ranks Open Interest relative to historical data

#### Gamma Rank  
```excel
=PERCENTRANK($K$2:$K$1000,K2,3)
```
**Implementation**: `gamma_rank = pl.col("gamma").rank("ordinal") / pl.col("gamma").count()`
**Purpose**: Ranks Gamma values for volatility assessment

#### Delta Rank
```excel
=PERCENTRANK($J$2:$J$1000,J2,3)
```
**Implementation**: `delta_rank = pl.col("delta").rank("ordinal") / pl.col("delta").count()`
**Purpose**: Ranks Delta values for directional exposure

#### Theta Rank
```excel
=PERCENTRANK($L$2:$L$1000,L2,3)
```
**Implementation**: `theta_rank = pl.col("theta").rank("ordinal") / pl.col("theta").count()`
**Purpose**: Ranks Theta values for time decay analysis

#### Vega Rank
```excel
=PERCENTRANK($M$2:$M$1000,M2,3)
```
**Implementation**: `vega_rank = pl.col("vega").rank("ordinal") / pl.col("vega").count()`
**Purpose**: Ranks Vega values for volatility sensitivity

#### IV Rank
```excel
=PERCENTRANK($N$2:$N$1000,N2,3)
```
**Implementation**: `iv_rank = pl.col("iv").rank("ordinal") / pl.col("iv").count()`
**Purpose**: Ranks Implied Volatility levels

#### Volume Rank
```excel
=PERCENTRANK($I$2:$I$1000,I2,3)
```
**Implementation**: `volume_rank = pl.col("volume").rank("ordinal") / pl.col("volume").count()`
**Purpose**: Ranks trading volume activity

### 2. Intrinsic Value Calculations

#### Basic Intrinsic Value
```excel
=IF(E2="CE",MAX(F2-D2,0),IF(E2="PE",MAX(D2-F2,0),0))
```
**Implementation**: 
```python
intrinsic_value = pl.when(pl.col("expiry_type") == "CE")
    .then(pl.max_horizontal([pl.col("current_price") - pl.col("strike"), pl.lit(0)]))
    .when(pl.col("expiry_type") == "PE")
    .then(pl.max_horizontal([pl.col("strike") - pl.col("current_price"), pl.lit(0)]))
    .otherwise(pl.lit(0))
```
**Purpose**: Calculates intrinsic value for Call/Put options

#### Intrinsic Value Rank
```excel
=PERCENTRANK($O$2:$O$1000,O2,3)
```
**Implementation**: `intrinsic_value_rank = pl.col("intrinsic_value").rank("ordinal") / pl.col("intrinsic_value").count()`
**Purpose**: Ranks intrinsic values across options

#### Premium Rank
```excel
=PERCENTRANK($G$2:$G$1000,G2,3)
```
**Implementation**: `premium_rank = pl.col("last_price").rank("ordinal") / pl.col("last_price").count()`
**Purpose**: Ranks option premiums

### 3. Pattern Detection Indicators

#### Consecutive Price Drop
```excel
=IF(AND(G2<G1,G1<G0),1,0)
```
**Implementation**: 
```python
consecutive_price_drop = (
    (pl.col("last_price") < pl.col("last_price").shift(1)) &
    (pl.col("last_price").shift(1) < pl.col("last_price").shift(2))
).cast(pl.Int32)
```
**Purpose**: Detects 3 consecutive price decreases

#### Consecutive OI Build
```excel
=IF(AND(H2>H1,H1>H0),1,0)
```
**Implementation**: 
```python
consecutive_oi_build = (
    (pl.col("oi") > pl.col("oi").shift(1)) &
    (pl.col("oi").shift(1) > pl.col("oi").shift(2))
).cast(pl.Int32)
```
**Purpose**: Detects 3 consecutive OI increases

#### Candle Dip
```excel
=IF((G2-G1)/G1<-0.02,1,0)
```
**Implementation**: 
```python
candle_dip = (
    ((pl.col("last_price") - pl.col("last_price").shift(1)) / pl.col("last_price").shift(1)) < -0.02
).cast(pl.Int32)
```
**Purpose**: Detects significant price drops (>2%)

#### Candle Rise
```excel
=IF((G2-G1)/G1>0.02,1,0)
```
**Implementation**: 
```python
candle_rise = (
    ((pl.col("last_price") - pl.col("last_price").shift(1)) / pl.col("last_price").shift(1)) > 0.02
).cast(pl.Int32)
```
**Purpose**: Detects significant price rises (>2%)

### 4. Trading Signals

#### BUY Signal 1
```excel
=IF(AND(P2>0.8,Q2>0.8,R2>0.8),1,0)
```
**Implementation**: 
```python
buy_signal_1 = (
    (pl.col("oi_rank") > 0.8) &
    (pl.col("gamma_rank") > 0.8) &
    (pl.col("delta_rank") > 0.8)
).cast(pl.Int32)
```
**Purpose**: Strong bullish signal based on high rankings

#### BUY Signal 2
```excel
=IF(AND(P2>0.6,T2>0.6,U2>0.6),1,0)
```
**Implementation**: 
```python
buy_signal_2 = (
    (pl.col("oi_rank") > 0.6) &
    (pl.col("theta_rank") > 0.6) &
    (pl.col("vega_rank") > 0.6)
).cast(pl.Int32)
```
**Purpose**: Moderate bullish signal

#### SELL Signal 1
```excel
=IF(AND(P2<0.2,Q2<0.2,R2<0.2),1,0)
```
**Implementation**: 
```python
sell_signal_1 = (
    (pl.col("oi_rank") < 0.2) &
    (pl.col("gamma_rank") < 0.2) &
    (pl.col("delta_rank") < 0.2)
).cast(pl.Int32)
```
**Purpose**: Strong bearish signal based on low rankings

### 5. Special Conditions

#### Gamma Move
```excel
=IF(Q2>0.9,1,0)
```
**Implementation**: 
```python
gamma_move = (pl.col("gamma_rank") > 0.9).cast(pl.Int32)
```
**Purpose**: Detects extreme gamma conditions

#### IV-Vega Condition
```excel
=IF(AND(V2>0.8,U2>0.8),1,0)
```
**Implementation**: 
```python
iv_vega_condition = (
    (pl.col("iv_rank") > 0.8) &
    (pl.col("vega_rank") > 0.8)
).cast(pl.Int32)
```
**Purpose**: High volatility environment detection

#### Gamma Condition
```excel
=IF(AND(Q2>0.7,R2>0.7),1,0)
```
**Implementation**: 
```python
gamma_condition = (
    (pl.col("gamma_rank") > 0.7) &
    (pl.col("delta_rank") > 0.7)
).cast(pl.Int32)
```
**Purpose**: Combined gamma-delta signal

## Implementation Notes

### 1. PERCENTRANK Conversion
Excel's `PERCENTRANK` function is implemented using Polars' `rank()` and `count()`:
```python
percentrank = pl.col("column").rank("ordinal") / pl.col("column").count()
```

### 2. Rolling Windows
For time-series analysis, rolling windows are used:
```python
rolling_avg = pl.col("column").rolling_mean(window_size=20)
```

### 3. Conditional Logic
Excel's `IF` statements are converted to Polars' `when().then().otherwise()`:
```python
result = pl.when(condition).then(value_if_true).otherwise(value_if_false)
```

### 4. Missing Data Handling
The system handles missing data gracefully:
- Uses `fill_null()` for missing values
- Implements minimum data requirements for calculations
- Provides warnings for insufficient data

## Configuration

All thresholds and parameters are configurable in `config.yaml`:
```yaml
indicators:
  custom:
    trading_signals:
      thresholds:
        candle_dip: -2.0
        candle_rise: 2.0
        high_rank_threshold: 0.8
        low_rank_threshold: 0.2
```

## Testing and Validation

To verify formula accuracy:
1. Compare outputs with original Excel calculations
2. Use the test script: `python test_with_real_data.py`
3. Check logs for any calculation warnings
4. Validate against known market scenarios

## Performance Considerations

- **Vectorized Operations**: All formulas use Polars vectorized operations
- **Memory Efficiency**: Lazy evaluation where possible
- **Parallel Processing**: Partitioned calculations for large datasets
- **Caching**: Intermediate results cached for complex formulas

## Known Limitations

1. **Interdependent Formulas**: Some formulas depend on others (e.g., "YES", "ints_value_rise")
2. **Historical Data Requirements**: Some indicators need sufficient historical data
3. **Market Hours**: Calculations respect market trading hours
4. **Data Quality**: Results depend on clean, accurate input data
