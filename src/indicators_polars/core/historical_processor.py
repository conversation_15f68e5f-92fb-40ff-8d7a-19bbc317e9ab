"""
Historical processing mode with progressing time windows
"""

import logging
from typing import Optional
from datetime import datetime, date, timedelta
import polars as pl

from .config_manager import ConfigManager
from .indicator_engine import IndicatorEngine
from ..database.clickhouse_client import ClickHouseClient
from ..utils.monitoring import PerformanceMonitor


logger = logging.getLogger(__name__)


class HistoricalProcessor:
    """Process historical data with progressing time windows"""
    
    def __init__(
        self, 
        config_manager: ConfigManager,
        clickhouse_client: ClickHouseClient,
        indicator_engine: IndicatorEngine,
        performance_monitor: PerformanceMonitor
    ):
        self.config = config_manager
        self.db_client = clickhouse_client
        self.indicator_engine = indicator_engine
        self.monitor = performance_monitor
    
    def run_historical_analysis(self, target_date: Optional[date] = None) -> bool:
        """
        Run historical analysis for a specific date
        
        Args:
            target_date: Date to analyze (defaults to config start_date)
        """
        if target_date is None:
            target_date = self.config.get_processing_date()
        
        logger.info(f"Starting historical analysis for {target_date}")
        
        with self.monitor.track_operation("historical_analysis", 0) as metrics:
            try:
                # Check if table exists
                table_name = self.db_client.get_table_name(target_date)
                if not self.db_client.table_exists(table_name):
                    logger.error(f"Table {table_name} does not exist")
                    return False
                
                # Create output table
                output_table = self.config.output.table_name
                if self.config.output.create_table:
                    self.db_client.create_output_table(output_table)
                
                # Fetch data for the entire day
                full_day_data = self.db_client.get_data_for_date(
                    target_date=target_date,
                    symbols=self.config.market.symbols,
                    start_time=self.config.market.start_time,
                    end_time=self.config.market.end_time
                )
                
                if full_day_data.is_empty():
                    logger.warning(f"No data found for {target_date}")
                    return False
                
                logger.info(f"Loaded {len(full_day_data)} rows for historical analysis")
                metrics.rows_processed = len(full_day_data)
                
                
                # Run progressive calculation
                result_df = self.indicator_engine.calculate_historical_progressive(
                    full_day_data,
                    start_time=self.config.market.start_time,
                    end_time=self.config.market.end_time
                )
                
                if result_df.is_empty():
                    logger.warning("No indicators calculated")
                    return False
                
                # Save results
                success = self.db_client.insert_indicators_data(
                    result_df, 
                    output_table,
                    self.config.output.batch_insert_size
                )
                
                if success:
                    logger.info(f"Historical analysis completed successfully for {target_date}")
                    return True
                else:
                    logger.error("Failed to save historical analysis results")
                    return False
                
            except Exception as e:
                logger.error(f"Error in historical analysis: {e}")
                metrics.errors_count += 1
                return False
    
    def run_historical_range(self, start_date: date, end_date: Optional[date] = None) -> bool:
        """
        Run historical analysis for a date range
        
        Args:
            start_date: Start date for analysis
            end_date: End date for analysis (defaults to start_date)
        """
        if end_date is None:
            end_date = start_date
        
        logger.info(f"Starting historical range analysis from {start_date} to {end_date}")
        
        current_date = start_date
        success_count = 0
        total_count = 0
        
        while current_date <= end_date:
            # Skip weekends (assuming market is closed)
            if current_date.weekday() < 5:  # Monday = 0, Sunday = 6
                total_count += 1
                logger.info(f"Processing date {current_date} ({total_count})")
                
                if self.run_historical_analysis(current_date):
                    success_count += 1
                else:
                    logger.warning(f"Failed to process {current_date}")
            else:
                logger.debug(f"Skipping weekend date {current_date}")
            
            current_date += timedelta(days=1)
        
        logger.info(f"Historical range analysis completed: {success_count}/{total_count} days processed successfully")
        return success_count == total_count
    
    def run_progressive_intraday_analysis(self, target_date: date, time_interval_minutes: int = 1) -> bool:
        """
        Run progressive intraday analysis simulating real-time processing
        
        This method simulates how indicators would be calculated during the trading day,
        processing data minute by minute with progressing windows.
        
        Args:
            target_date: Date to analyze
            time_interval_minutes: Time interval for progressive analysis
        """
        logger.info(f"Starting progressive intraday analysis for {target_date}")
        
        with self.monitor.track_operation("progressive_intraday", 0) as metrics:
            try:
                # Check if table exists
                table_name = self.db_client.get_table_name(target_date)
                if not self.db_client.table_exists(table_name):
                    logger.error(f"Table {table_name} does not exist")
                    return False
                
                # Create output table
                output_table = self.config.output.table_name
                if self.config.output.create_table:
                    self.db_client.create_output_table(output_table)
                
                # Get market start and end times
                market_start = datetime.strptime(self.config.market.start_time, '%H:%M').time()
                market_end = datetime.strptime(self.config.market.end_time, '%H:%M').time()
                
                # Create timezone-aware datetimes
                tz = self.config.get_timezone()
                start_datetime = tz.localize(datetime.combine(target_date, market_start))
                end_datetime = tz.localize(datetime.combine(target_date, market_end))
                
                # Generate time intervals
                current_time = start_datetime
                interval_delta = timedelta(minutes=time_interval_minutes)
                
                all_results = []
                processed_count = 0
                
                while current_time <= end_datetime:
                    # Fetch data from market start to current time
                    progressive_data = self.db_client.get_data_for_date(
                        target_date=target_date,
                        symbols=self.config.market.symbols,
                        start_time=self.config.market.start_time,
                        end_time=current_time.strftime('%H:%M')
                    )
                    
                    if not progressive_data.is_empty():
                        # Calculate indicators for this progressive window
                        result_df = self.indicator_engine.calculate_indicators(
                            progressive_data, 
                            delta_only=False
                        )
                        
                        # Filter to only current time results (handle timezone comparison)
                        current_results = result_df.filter(
                            pl.col("timestamp").dt.replace_time_zone(None) == current_time.replace(tzinfo=None)
                        )
                        
                        if not current_results.is_empty():
                            all_results.append(current_results)
                            processed_count += len(current_results)
                    
                    current_time += interval_delta
                    
                    # Log progress every 30 minutes
                    if current_time.minute % 30 == 0:
                        logger.info(f"Processed up to {current_time.strftime('%H:%M')}")
                
                # Combine and save all results
                if all_results:
                    final_results = pl.concat(all_results, how="vertical")
                    
                    success = self.db_client.insert_indicators_data(
                        final_results,
                        output_table,
                        self.config.output.batch_insert_size
                    )
                    
                    metrics.rows_processed = processed_count
                    
                    if success:
                        logger.info(f"Progressive intraday analysis completed: {processed_count} rows processed")
                        return True
                    else:
                        logger.error("Failed to save progressive analysis results")
                        return False
                else:
                    logger.warning("No data processed in progressive analysis")
                    return False
                
            except Exception as e:
                logger.error(f"Error in progressive intraday analysis: {e}")
                metrics.errors_count += 1
                return False
    
    def validate_historical_setup(self, target_date: date) -> bool:
        """Validate that historical processing can be run for the target date"""
        try:
            # Check table existence
            table_name = self.db_client.get_table_name(target_date)
            if not self.db_client.table_exists(table_name):
                logger.error(f"Source table {table_name} does not exist")
                return False
            
            # Check if there's data for the symbols
            test_data = self.db_client.get_data_for_date(
                target_date=target_date,
                symbols=self.config.market.symbols[:1],  # Test with first symbol
                start_time=self.config.market.start_time,
                end_time=self.config.market.start_time  # Just check start time
            )
            
            if test_data.is_empty():
                logger.error(f"No data found for {target_date} at market start time")
                return False
            
            logger.info(f"Historical setup validation passed for {target_date}")
            return True
            
        except Exception as e:
            logger.error(f"Historical setup validation failed: {e}")
            return False
